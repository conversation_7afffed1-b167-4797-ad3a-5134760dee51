#!/usr/bin/env python3
"""
测试animate_markdown.py重构后的功能

验证重构后的animate_markdown.py是否正确使用了新的text_style_system。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 测试导入
try:
    from dsl.v2.animation_functions.animate_markdown import (
        parse_text_styles,
        find_styled_text_in_mobject,
        apply_style_animations_to_text,
        create_text_with_emoji_unified
    )
    print("✅ animate_markdown.py 导入成功")
except ImportError as e:
    print(f"❌ animate_markdown.py 导入失败: {e}")
    sys.exit(1)

# 测试新的text_style_system导入
try:
    from dsl.v2.utils.text_style_system import (
        create_styled_text,
        has_styled_text_content,
        apply_text_animations
    )
    print("✅ text_style_system 导入成功")
except ImportError as e:
    print(f"❌ text_style_system 导入失败: {e}")
    sys.exit(1)


def test_parse_text_styles_compatibility():
    """测试parse_text_styles函数的兼容性"""
    print("\n" + "=" * 50)
    print("测试 parse_text_styles 兼容性")
    print("=" * 50)
    
    test_cases = [
        "这是**加粗**的文本",
        "包含*斜体*和`代码`",
        "数学公式 $E=mc^2$ 测试",
        "普通文本没有格式",
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: '{text}'")
        try:
            clean_text, styles = parse_text_styles(text)
            print(f"  纯文本: '{clean_text}'")
            print(f"  样式信息: {styles}")
            print(f"  ✅ 解析成功")
        except Exception as e:
            print(f"  ❌ 解析失败: {e}")


def test_text_creation_methods():
    """测试不同的文本创建方法"""
    print("\n" + "=" * 50)
    print("测试文本创建方法")
    print("=" * 50)
    
    test_text = "这是**重要**的`代码示例`"
    
    # 测试原有方法
    print(f"测试文本: '{test_text}'")
    
    try:
        # 方法1: 使用原有的create_text_with_emoji_unified
        print("\n方法1: create_text_with_emoji_unified")
        clean_text, styles = parse_text_styles(test_text)
        text_obj_old = create_text_with_emoji_unified(clean_text, 36, "#FFFFFF")
        print(f"  创建成功: {type(text_obj_old)}")
        print(f"  文本内容: '{getattr(text_obj_old, 'text', 'N/A')}'")
        
        # 手动附加样式
        if styles and hasattr(text_obj_old, 'text'):
            text_obj_old.text_styles = styles
            text_obj_old.original_clean_text = clean_text
            print(f"  样式附加: {list(styles.keys())}")
        
    except Exception as e:
        print(f"  ❌ 原有方法失败: {e}")
    
    try:
        # 方法2: 使用新的create_styled_text
        print("\n方法2: create_styled_text")
        text_obj_new = create_styled_text(
            markdown_text=test_text,
            font_size=36,
            font_color="#FFFFFF"
        )
        print(f"  创建成功: {type(text_obj_new)}")
        print(f"  文本内容: '{text_obj_new.text}'")
        
        # 检查样式信息
        if hasattr(text_obj_new, 'text_styles'):
            print(f"  样式信息: {list(text_obj_new.text_styles.keys())}")
        
        # 检查是否有样式内容
        has_styles = has_styled_text_content(text_obj_new)
        print(f"  有样式内容: {has_styles}")
        
    except Exception as e:
        print(f"  ❌ 新方法失败: {e}")


def test_style_detection():
    """测试样式检测功能"""
    print("\n" + "=" * 50)
    print("测试样式检测功能")
    print("=" * 50)
    
    test_cases = [
        ("普通文本", False),
        ("这是**加粗**文本", True),
        ("包含`代码`的文本", True),
        ("数学公式 $x^2$ 测试", True),
        ("emoji测试 😀", False),
    ]
    
    for text, expected_has_styles in test_cases:
        print(f"\n测试: '{text}'")
        try:
            # 使用新的样式系统创建文本
            text_obj = create_styled_text(
                markdown_text=text,
                font_size=32,
                font_color="#FFFFFF"
            )
            
            # 检测是否有样式
            has_styles = has_styled_text_content(text_obj)
            
            print(f"  期望有样式: {expected_has_styles}")
            print(f"  实际有样式: {has_styles}")
            
            if has_styles == expected_has_styles:
                print(f"  ✅ 检测正确")
            else:
                print(f"  ⚠️  检测不符合期望")
                
            # 显示样式信息
            if hasattr(text_obj, 'text_styles'):
                print(f"  样式类型: {list(text_obj.text_styles.keys())}")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n" + "=" * 50)
    print("测试向后兼容性")
    print("=" * 50)
    
    # 测试原有的函数是否仍然可用
    functions_to_test = [
        ("parse_text_styles", parse_text_styles),
        ("find_styled_text_in_mobject", find_styled_text_in_mobject),
        ("apply_style_animations_to_text", apply_style_animations_to_text),
        ("create_text_with_emoji_unified", create_text_with_emoji_unified),
    ]
    
    for func_name, func in functions_to_test:
        print(f"\n测试函数: {func_name}")
        try:
            # 简单的调用测试
            if func_name == "parse_text_styles":
                result = func("测试**文本**")
                print(f"  ✅ 函数可调用，返回: {type(result)}")
            elif func_name == "create_text_with_emoji_unified":
                result = func("测试文本", 32, "#FFFFFF")
                print(f"  ✅ 函数可调用，返回: {type(result)}")
            else:
                print(f"  ✅ 函数存在且可导入")
                
        except Exception as e:
            print(f"  ❌ 函数测试失败: {e}")


def main():
    """运行所有测试"""
    print("Animate Markdown 重构测试")
    print("=" * 60)
    
    try:
        test_parse_text_styles_compatibility()
        test_text_creation_methods()
        test_style_detection()
        test_backward_compatibility()
        
        print("\n" + "=" * 60)
        print("所有重构测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
