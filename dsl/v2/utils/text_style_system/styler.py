"""
Text Style Styler - 样式标记模块

负责将样式信息附加到Text对象，为后续的动画处理做准备。
"""

from __future__ import annotations
from typing import Dict, List, Optional, Union
from loguru import logger
from manim import *

from .parser import parse_text_styles


def attach_styles_to_text(text_obj: Text, styles: Dict[str, List[str]],
                         original_clean_text: Optional[str] = None) -> Text:
    """
    将样式信息附加到Text对象

    Args:
        text_obj: Manim Text对象
        styles: 样式信息字典，如 {"bold": ["加粗文本"], "italic": ["斜体文本"]}
        original_clean_text: 原始的纯文本内容（可选）

    Returns:
        附加了样式信息的Text对象
    """
    if not isinstance(text_obj, Text):
        logger.warning(f"传入的对象不是Text类型: {type(text_obj)}")
        return text_obj

    if not styles:
        logger.debug("没有样式信息需要附加")
        return text_obj

    try:
        # 将样式信息附加到Text对象
        text_obj.text_styles = styles

        # 如果提供了原始纯文本，也保存起来
        if original_clean_text is not None:
            text_obj.original_clean_text = original_clean_text

        logger.debug(f"成功附加样式信息到Text对象: {list(styles.keys())}")

    except Exception as e:
        logger.error(f"附加样式信息时出错: {e}")

    return text_obj


def create_text_with_styles(markdown_text: str,
                           font_size: int = 48,
                           font_color: Union[str, ManimColor] = WHITE,
                           **text_kwargs) -> Text:
    """
    从markdown文本创建带样式信息的Text对象

    Args:
        markdown_text: 包含markdown格式的文本
        font_size: 字体大小
        font_color: 字体颜色
        **text_kwargs: 传递给Text构造函数的其他参数

    Returns:
        带有样式信息的Text对象
    """
    if not markdown_text:
        logger.warning("传入的markdown文本为空")
        return Text("", font_size=font_size, color=font_color, **text_kwargs)

    try:
        # 解析markdown文本
        clean_text, styles = parse_text_styles(markdown_text)

        # 创建Text对象
        text_obj = Text(clean_text, font_size=font_size, color=font_color, **text_kwargs)

        # 附加样式信息
        text_obj = attach_styles_to_text(text_obj, styles, clean_text)

        logger.debug(f"成功创建带样式的Text对象: '{clean_text[:50]}...'")

        return text_obj

    except Exception as e:
        logger.error(f"创建带样式Text对象时出错: {e}")
        # 出错时返回基本的Text对象
        return Text(markdown_text, font_size=font_size, color=font_color, **text_kwargs)


def batch_attach_styles(text_objects: List[Text],
                       styles_list: List[Dict[str, List[str]]],
                       clean_texts: Optional[List[str]] = None) -> List[Text]:
    """
    批量为多个Text对象附加样式信息

    Args:
        text_objects: Text对象列表
        styles_list: 对应的样式信息列表
        clean_texts: 对应的纯文本列表（可选）

    Returns:
        附加了样式信息的Text对象列表
    """
    if len(text_objects) != len(styles_list):
        logger.error(f"Text对象数量({len(text_objects)})与样式信息数量({len(styles_list)})不匹配")
        return text_objects

    if clean_texts and len(clean_texts) != len(text_objects):
        logger.error(f"纯文本数量({len(clean_texts)})与Text对象数量不匹配")
        clean_texts = None

    result_objects = []

    for i, (text_obj, styles) in enumerate(zip(text_objects, styles_list)):
        clean_text = clean_texts[i] if clean_texts else None
        styled_obj = attach_styles_to_text(text_obj, styles, clean_text)
        result_objects.append(styled_obj)

    logger.debug(f"批量处理完成，共处理{len(result_objects)}个Text对象")

    return result_objects


def has_styles(text_obj: Text) -> bool:
    """
    检查Text对象是否包含样式信息

    Args:
        text_obj: 要检查的Text对象

    Returns:
        如果包含样式信息返回True，否则返回False
    """
    return hasattr(text_obj, "text_styles") and bool(text_obj.text_styles)


def get_styles(text_obj: Text) -> Dict[str, List[str]]:
    """
    获取Text对象的样式信息

    Args:
        text_obj: Text对象

    Returns:
        样式信息字典，如果没有样式信息则返回空字典
    """
    if has_styles(text_obj):
        return text_obj.text_styles
    return {}


def get_style_types(text_obj: Text) -> List[str]:
    """
    获取Text对象包含的样式类型列表

    Args:
        text_obj: Text对象

    Returns:
        样式类型列表，如 ["bold", "italic"]
    """
    styles = get_styles(text_obj)
    return list(styles.keys())


def get_styled_content(text_obj: Text, style_type: str) -> List[str]:
    """
    获取Text对象中特定样式类型的内容

    Args:
        text_obj: Text对象
        style_type: 样式类型，如 "bold", "italic"

    Returns:
        该样式类型的内容列表
    """
    styles = get_styles(text_obj)
    return styles.get(style_type, [])


def remove_styles(text_obj: Text) -> Text:
    """
    移除Text对象的样式信息

    Args:
        text_obj: Text对象

    Returns:
        移除样式信息后的Text对象
    """
    if hasattr(text_obj, "text_styles"):
        delattr(text_obj, "text_styles")

    if hasattr(text_obj, "original_clean_text"):
        delattr(text_obj, "original_clean_text")

    logger.debug("已移除Text对象的样式信息")

    return text_obj


def copy_styles(source_text: Text, target_text: Text) -> Text:
    """
    将一个Text对象的样式信息复制到另一个Text对象

    Args:
        source_text: 源Text对象
        target_text: 目标Text对象

    Returns:
        复制了样式信息的目标Text对象
    """
    if not has_styles(source_text):
        logger.debug("源Text对象没有样式信息可复制")
        return target_text

    try:
        styles = get_styles(source_text)
        original_text = getattr(source_text, "original_clean_text", None)

        target_text = attach_styles_to_text(target_text, styles, original_text)

        logger.debug("成功复制样式信息")

    except Exception as e:
        logger.error(f"复制样式信息时出错: {e}")

    return target_text


def merge_styles(text_obj: Text, additional_styles: Dict[str, List[str]]) -> Text:
    """
    将额外的样式信息合并到Text对象的现有样式中

    Args:
        text_obj: Text对象
        additional_styles: 要合并的额外样式信息

    Returns:
        合并了样式信息的Text对象
    """
    if not additional_styles:
        return text_obj

    try:
        current_styles = get_styles(text_obj)

        # 合并样式信息
        for style_type, content_list in additional_styles.items():
            if style_type in current_styles:
                # 合并到现有样式类型
                current_styles[style_type].extend(content_list)
                # 去重
                current_styles[style_type] = list(set(current_styles[style_type]))
            else:
                # 添加新的样式类型
                current_styles[style_type] = content_list

        # 重新附加合并后的样式
        text_obj.text_styles = current_styles

        logger.debug(f"成功合并样式信息: {list(additional_styles.keys())}")

    except Exception as e:
        logger.error(f"合并样式信息时出错: {e}")

    return text_obj
