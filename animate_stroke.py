# animate_color_stroke_fixed.py
import xml.etree.ElementTree as ET
import os

def create_color_stroke_animation(svg_input_path="color_output_hires.svg", html_output_path="animated_color_stroke.html"):
    """
    为多层彩色 SVG 创建一个顺序描边绘制动画。
    此版本修正了尺寸问题，确保最终动画保持原始图像大小。
    """
    if not os.path.exists(svg_input_path):
        print(f"错误: 输入文件 '{svg_input_path}' 不存在。")
        print("请先运行 vectorize_color_hires.py 来生成它。")
        return

    try:
        namespace = {'svg': 'http://www.w3.org/2000/svg'}
        tree = ET.parse(svg_input_path)
        root = tree.getroot()
        
        # --- 【核心改动】 ---
        # 同时获取 width, height 和 viewBox
        width = root.get('width')
        height = root.get('height')
        viewBox = root.get('viewBox') # 读取 viewBox 属性
        # --- 结束改动 ---

        path_elements = root.findall('svg:path', namespace)
    except ET.ParseError:
        print(f"错误: '{svg_input_path}' 不是一个有效的 SVG 文件。")
        return

    paths_data = [{'d': p.get('d'), 'color': p.get('fill', 'black')} for p in path_elements]
    
    # 如果 viewBox 不存在，则不添加该属性
    viewBox_attribute = f'viewBox="{viewBox}"' if viewBox else ''

    html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>彩色 SVG 描边动画 (尺寸已修复)</title>
    <style>
        body {{ display: flex; justify-content: center; align-items: center; height: 100vh; background-color: #f0f0f0; }}
        svg {{ border: 1px solid #ccc; background-color: white; }}
        @keyframes draw-in {{
            to {{ stroke-dashoffset: 0; }}
        }}
        .drawable-path {{
            animation: draw-in 2s ease-in-out forwards;
        }}
    </style>
</head>
<body>
    <!-- --- 【核心改动】 --- -->
    <!-- 在 SVG 标签中同时写入 width, height 和 viewBox -->
    <svg width="{width}" height="{height}" {viewBox_attribute} xmlns="http://www.w3.org/2000/svg">
    <!-- --- 结束改动 --- -->
        {''.join([f'<path class="drawable-path" d="{p["d"]}" fill="none" stroke="{p["color"]}" stroke-width="1"/>' for p in paths_data])}
    </svg>
    <script>
      document.addEventListener('DOMContentLoaded', () => {{
        const paths = document.querySelectorAll('.drawable-path');
        paths.forEach((path, index) => {{
          const length = path.getTotalLength();
          path.style.strokeDasharray = length;
          path.style.strokeDashoffset = length;
          
          // 每个路径的动画在前一个开始 0.25 秒后开始
          const delay = index * 0.5; 
          path.style.animationDelay = `${{delay}}s`;
        }});
      }});
    </script>
</body>
</html>
"""

    with open(html_output_path, 'w', encoding='utf-8') as f:
        f.write(html_template)
    print(f"成功创建尺寸已修复的描边动画文件: '{html_output_path}'")

if __name__ == '__main__':
    # 确保这个脚本读取的是高分辨率版本生成的 SVG
    create_color_stroke_animation(svg_input_path="color_output_hires.svg")

