# vectorize_image_final.py
import potrace
from PIL import Image, ImageDraw
import numpy as np
import os

def convert_image_to_svg(input_image_path, output_svg_path):
  """
  将一张图片转换为黑白的 SVG 轮廓。
  此版本通过合并路径和使用 fill-rule="evenodd" 来正确处理孔洞。
  """
  if not os.path.exists(input_image_path):
      print(f"错误：输入文件未找到，请确认 '{input_image_path}' 路径正确。")
      return

  image = Image.open(input_image_path)  # .convert('1')
  width, height = image.size
  data = np.array(image)

  bitmap = potrace.Bitmap(data)
  path = bitmap.trace()

  # 【核心改动】将所有路径数据合并到一个字符串中
  # 我们将生成一个带孔洞的、单一的复杂路径
  all_paths_data = ""
  for curve in path:
      # 为每个子路径（轮廓或孔洞）构建路径数据
      start_point = curve.start_point
      all_paths_data += f"M{start_point[0]},{start_point[1]}"
      for segment in curve:
          end_point = segment.end_point
          if segment.is_corner:
              c = segment.c
              all_paths_data += f"L{c[0]},{c[1]}L{end_point[0]},{end_point[1]}"
          else:
              c1 = segment.c1
              c2 = segment.c2
              all_paths_data += f"C{c1[0]},{c1[1]} {c2[0]},{c2[1]} {end_point[0]},{end_point[1]}"
      all_paths_data += "Z" # 'Z' 表示闭合当前子路径

  # 5. 将合并后的路径数据写入 SVG 文件
  with open(output_svg_path, "w") as fp:
      # SVG 容器
      fp.write(f'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" '
               f'width="{width}" height="{height}">')
      
      # 【核心改动】写入单一的 <path>，并添加 fill-rule="evenodd"
      # fill="black" 明确指定填充色
      fp.write(f'<path fill-rule="evenodd" fill="black" d="{all_paths_data}"/>')
          
      fp.write('</svg>')
      
  print(f"成功将 '{input_image_path}' 转换为 '{output_svg_path}'，并正确处理了文字孔洞。")


if __name__ == '__main__':
  input_file = 'your_image.png'
  output_file = 'output.svg'
  
  # # 创建一个黑底白字的图片用于测试
  # img = Image.new('RGB', (150, 50), color = 'black') # 背景是黑色
  # draw = ImageDraw.Draw(img)
  # # 在黑色背景上写白色的字
  # draw.text((18, 15), "Hello SVG", fill='white')
  # img.save(input_file)

  print(f"创建了一个名为 '{input_file}' 的黑底白字示例图片。")
  convert_image_to_svg(input_file, output_file)

