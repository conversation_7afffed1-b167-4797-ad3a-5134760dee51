# animate_color_fill_fixed.py
import xml.etree.ElementTree as ET
import os

def create_color_fill_animation(svg_input_path="color_output_hires.svg", html_output_path="animated_color_fill.html"):
    """
    为多层彩色 SVG 创建一个顺序填充（淡入）动画。
    此版本修正了尺寸问题，确保最终动画保持原始图像大小。
    """
    if not os.path.exists(svg_input_path):
        print(f"错误: 输入文件 '{svg_input_path}' 不存在。")
        print("请先运行 vectorize_color_hires.py 来生成它。")
        return

    try:
        namespace = {'svg': 'http://www.w3.org/2000/svg'}
        tree = ET.parse(svg_input_path)
        root = tree.getroot()
        
        # --- 【核心改动】 ---
        # 同时获取 width, height 和 viewBox
        width = root.get('width')
        height = root.get('height')
        viewBox = root.get('viewBox') # 读取 viewBox 属性
        # --- 结束改动 ---

        path_elements = root.findall('svg:path', namespace)
    except ET.ParseError:
        print(f"错误: '{svg_input_path}' 不是一个有效的 SVG 文件。")
        return

    paths_data = [{'d': p.get('d'), 'color': p.get('fill', 'black')} for p in path_elements]

    # 如果 viewBox 不存在（例如处理的是旧的、未缩放的 SVG），则不添加该属性
    viewBox_attribute = f'viewBox="{viewBox}"' if viewBox else ''

    html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>彩色 SVG 填充动画 (尺寸已修复)</title>
    <style>
        body {{ display: flex; justify-content: center; align-items: center; height: 100vh; background-color: #f0f0f0; }}
        svg {{ border: 1px solid #ccc; background-color: white; }}
        @keyframes fade-in {{
            from {{ opacity: 0; }}
            to {{ opacity: 1; }}
        }}
        .fillable-path {{
            opacity: 0;
            animation: fade-in 0.5s ease-in forwards;
        }}
    </style>
</head>
<body>
    <!-- --- 【核心改动】 --- -->
    <!-- 在 SVG 标签中同时写入 width, height 和 viewBox -->
    <svg width="{width}" height="{height}" {viewBox_attribute} xmlns="http://www.w3.org/2000/svg">
    <!-- --- 结束改动 --- -->
        {''.join([f'<path class="fillable-path" d="{p["d"]}" fill="{p["color"]}" fill-rule="evenodd"/>' for p in paths_data])}
    </svg>
    <script>
      document.addEventListener('DOMContentLoaded', () => {{
        const paths = document.querySelectorAll('.fillable-path');
        paths.forEach((path, index) => {{
          const delay = index * 0.15; // 可以调整延迟来看不同效果
          path.style.animationDelay = `${{delay}}s`;
        }});
      }});
    </script>
</body>
</html>
"""
    with open(html_output_path, 'w', encoding='utf-8') as f:
        f.write(html_template)
    print(f"成功创建尺寸已修复的填充动画文件: '{html_output_path}'")

if __name__ == '__main__':
    # 确保这个脚本读取的是高分辨率版本生成的 SVG
    create_color_fill_animation(svg_input_path="color_output_hires.svg")

