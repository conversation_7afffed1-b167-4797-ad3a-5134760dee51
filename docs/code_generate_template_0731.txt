
# Manim代码生成规范

## 基础要求
**重要**写最有把握的代码，保证代码可运行
- 继承 `ProfessionalScienceTemplate` 类，重写 `construct` 方法
- 使用标准区域接口，保证内容区域完全遵循输入描述
  - 标题区域: self.create_title_region_content(string), 返回VGroup(Text())
  - 步骤区域: self.create_step_region_content(string), 返回VGroup(Text())
  - 主内容区域: self.create_main_region_content(VGroup), 返回VGroup
  - 辅助区域: self.create_right_auxiliary_content(VGroup), 返回VGroup
  - 结果区域: self.create_result_region_content(VGroup), 返回VGroup 

## 关键规范
**重要**禁止用`copy()`、`move_to()`和`Transform()`函数
1. **元素组织**：所有元素必须在 `create_main_region_content` 之前定义完成，包括后期出现的高亮框、说明文字等，并且都添加到self.region_elements['main']中用于退场
2. **布局定位**：VGroup内元素使用相对位置函数`next_to()`、`align_to()`、`arrange()`进行布局
3. **核心动作**：使用`ReplacementTransform()`、`FadeTransformPieces()`、`Rotate()`、`group.shift()`进行元素变化，非必要不使用`arrange()`函数
4. **文字限制**：标题≤8字，步骤≤12字，辅助标题≤6字，结果≤20字
5. **代码逻辑**：必须遵循代码模版的6个逻辑编写代码，1. 阶段开始时间戳记录，2.定义标题和步骤区域内容，3.定义主区域元素内容，4. 标题和步骤区域内容动画，5. 主内容区域动画，6.记录函数结束时各区域元素状态
6. **时间记录**：在阶段开始时必须通过log_animation_timestamp记录阶段开始的时间戳。

## 代码模板
```python
import sys, os
sys.path.insert(0, os.getcwd())
from prompts.professional_science_template import ProfessionalScienceTemplate

class MyExample(ProfessionalScienceTemplate):
    def construct(self):
        # 背景
        self.setup_background()
        # 阶段一
        self.create_stage1_content()
        # 阶段二
        self.create_stage2_content()

    def create_stage2_content(self):

        # 1. 阶段开始时间戳记录
        self.log_animation_timestamp("阶段记录：阶段二")

        # 2. 定义标题和步骤区域内容
        title = self.create_title_region_content("标题")
        step = self.create_step_region_content("步骤")

        # 3. 定义主区域元素内容
        // 阶段一新创建主区域元素
        main_group = VGroup(element1, element2, highlight_box)
        main = self.create_main_region_content(main_group)

        // 后续阶段则获取当前缓存的主区域元素，单层VGroup结构
        main_group = self.region_elements['main']
        // 使用缓存的主区域元素
        node = main_group[1]
        
        # 4. 标题和步骤区域内容动画
        self.play(ReplacementTransform(self.region_elements['title'], title))
        self.play(ReplacementTransform(self.region_elements['step'], step))

        # 5. 主内容区域动画
        // 主内容区域元素切换，Graph切换使用FadeTransformPieces
        self.play(ReplacementTransform(self.region_elements['main'], main))

        # 6.记录函数结束时各区域元素状态
        self.region_elements['title'] = title
        self.region_elements['step'] = step
        self.region_elements['main'] = main

    
```

## 数据结构实现规则
```python 
## 二叉树使用Graph实现，元素切换必须用FadeTransformPieces()函数
graph = Graph(
    vertices=[1, 2, 3],
    edges=[(1, 2), (1, 3)],
    labels={i: Text(chr(64 + i)) for i in range(1, 4)},
    layout={1: [0, 2, 0],2: [-2, 0, 0],3:[2, 0, 0]},
    layout_config={"root_vertex": 1},
    vertex_config={"radius": 0.5, "fill_color": BLUE_E},
    edge_config={"stroke_color": WHITE, "stroke_width": 2},
    label_fill_color=WHITE
)
```

## Manim重点函数说明
```python 
## 批量排列用arrange函数，a.arrange(DOWN, buff=0.2, aligned_edge=LEFT)
## 相对位置用next_to函数，a.next_to(b, UP)
## 旋转用Rotate函数，self.play(Rotate(group, 2*PI, about_point=ORIGIN))
## 坐标精准移动用shift函数，self.play(group.shift(LEFT * 1.5))
## 交换元素用Swap函数，self.play(Swap(a, b))，必须同时交换内容 a, b = b, a
## 相对位置移动用animate函数，self.play(a.animate.next_to(b, DOWN))
## 合并用ReplacementTransfor函数，self.play(ReplacementTransform(a, b))
```