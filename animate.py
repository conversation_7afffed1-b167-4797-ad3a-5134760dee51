# animate_svg.py
import xml.etree.ElementTree as ET
import os

def create_animation_html(svg_input_path="output.svg", html_output_path="animated_drawing.html"):
    """
    读取一个 potrace 生成的 SVG 文件，并创建一个包含描边动画的 HTML 文件。
    """
    # 1. 检查输入的 SVG 文件是否存在
    if not os.path.exists(svg_input_path):
        print(f"错误: 输入文件 '{svg_input_path}' 不存在。")
        print("请先运行上一个脚本 (vectorize_image_final.py) 来生成它。")
        return

    # 2. 解析 SVG 文件以获取路径数据和尺寸
    try:
        # SVG 文件使用命名空间，我们需要在查找元素时指定它
        namespace = {'svg': 'http://www.w3.org/2000/svg'}
        tree = ET.parse(svg_input_path)
        root = tree.getroot()
        
        # 获取 SVG 的宽度和高度
        width = root.get('width')
        height = root.get('height')

        # 找到 <path> 元素并获取其 'd' 属性（路径数据）
        path_element = root.find('svg:path', namespace)
        if path_element is None:
            print(f"错误: 在 '{svg_input_path}' 中没有找到 <path> 元素。")
            return
            
        path_data = path_element.get('d')

    except ET.ParseError:
        print(f"错误: '{svg_input_path}' 不是一个有效的 XML/SVG 文件。")
        return

    # 3. 定义 HTML、CSS 和 JavaScript 模板
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>SVG 路径绘制动画</title>
    <style>
        body {{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f4f4f9;
            font-family: sans-serif;
            color: #333;
        }}
        h1 {{
            margin-bottom: 20px;
        }}
        svg {{
            border: 1px solid #ccc;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }}
        /* 定义动画 */
        @keyframes draw-in {{
            /* 动画结束时，偏移量为 0，路径完全可见 */
            to {{
                stroke-dashoffset: 0;
            }}
        }}
        /* 将动画应用到我们的路径上 */
        #animated-path {{
            /* 动画名称、时长、缓动函数和最终状态 */
            animation: draw-in 30s ease-in-out forwards;
        }}
    </style>
</head>
<body>

    <h1>动画化 SVG</h1>

    <!-- 嵌入 SVG，并修改 path 属性 -->
    <svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
        <!--
          - id="animated-path": 让 JS 可以找到这个元素
          - fill="none": 移除填充，否则动画时内部也是黑的
          - stroke="black": 设置描边颜色
          - stroke-width="1": 设置描边宽度
        -->
        <path id="animated-path" d='{path_data}' 
              fill="none" stroke="black" stroke-width="1"/>
    </svg>

    <script>
      // 等待整个页面加载完成
      document.addEventListener('DOMContentLoaded', () => {{
        // 1. 通过 ID 获取路径元素
        const path = document.querySelector('#animated-path');
        
        if (path) {{
          // 2. 获取路径的总长度
          const pathLength = path.getTotalLength();
          
          console.log(`路径总长度: ${{pathLength}}px`);

          // 3. 设置初始状态：将路径变成“虚线”，并将其“隐藏”
          //    - stroke-dasharray: 创建一个和路径本身一样长的“划线”和“空白”
          //    - stroke-dashoffset: 将“划线”部分移出视野，只留下“空白”
          path.style.strokeDasharray = pathLength;
          path.style.strokeDashoffset = pathLength;
          
          // 4. CSS 的 @keyframes 动画会自动开始播放
          //    它会将 stroke-dashoffset 从 pathLength 动画到 0
          console.log('动画已启动！');
        }}
      }});
    </script>

</body>
</html>
"""

    # 4. 将生成的 HTML 内容写入文件
    with open(html_output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"成功创建动画文件: '{html_output_path}'")
    print("请用你的网络浏览器打开它来查看效果。")


if __name__ == '__main__':
    import sys
    create_animation_html(sys.argv[1])

