"""
Text Style Parser - 文本样式解析模块

负责解析markdown格式文本，提取纯文本和样式信息。
支持的样式类型：
- 粗体: **text**
- 斜体: *text*
- 行内代码: `code`
- 删除线: ~~text~~
- 链接: [text](url)
- 内联数学公式: $formula$
"""

from __future__ import annotations
import re
from typing import Dict, List, Tuple
from loguru import logger


def parse_text_styles(markdown_text: str) -> Tuple[str, Dict[str, List[str]]]:
    """
    从markdown文本中提取纯文本和样式信息

    Args:
        markdown_text: 原始markdown文本，如 "A line including `inline code` and **bold text**"

    Returns:
        tuple: (clean_text, styles)
        - clean_text: 纯文本，如 "A line including inline code and bold text"
        - styles: 样式信息字典，如 {"bold": ["bold text"], "code": ["inline code"]}

    Examples:
        >>> clean_text, styles = parse_text_styles("这是**加粗**和*斜体*的文本")
        >>> print(clean_text)  # "这是加粗和斜体的文本"
        >>> print(styles)      # {"bold": ["加粗"], "italic": ["斜体"]}
    """
    if not markdown_text:
        return "", {}

    clean_text = markdown_text
    styles = {
        "bold": [],
        "italic": [],
        "code": [],
        "strikethrough": [],
        "link": [],
        "math": []
    }

    try:
        # 处理内联数学公式 $...$，使用与mistune对齐的正则表达式
        INLINE_MATH_PATTERN = r"\$(?!\s)(?P<math_text>.+?)(?!\s)\$"
        for match in re.finditer(INLINE_MATH_PATTERN, clean_text):
            math_content = match.group("math_text")
            styles["math"].append(math_content)
        clean_text = re.sub(INLINE_MATH_PATTERN, lambda m: m.group("math_text"), clean_text)

        # 处理粗体 **text**
        bold_pattern = r"\*\*([^*]+)\*\*"
        for match in re.finditer(bold_pattern, clean_text):
            bold_content = match.group(1)
            styles["bold"].append(bold_content)
        clean_text = re.sub(bold_pattern, r"\1", clean_text)

        # 处理斜体 *text*
        italic_pattern = r"\*([^*]+)\*"
        for match in re.finditer(italic_pattern, clean_text):
            italic_content = match.group(1)
            styles["italic"].append(italic_content)
        clean_text = re.sub(italic_pattern, r"\1", clean_text)

        # 处理行内代码 `code`
        code_pattern = r"`([^`]+)`"
        for match in re.finditer(code_pattern, clean_text):
            code_content = match.group(1)
            styles["code"].append(code_content)
        clean_text = re.sub(code_pattern, r"\1", clean_text)

        # 处理删除线 ~~text~~
        strike_pattern = r"~~([^~]+)~~"
        for match in re.finditer(strike_pattern, clean_text):
            strike_content = match.group(1)
            styles["strikethrough"].append(strike_content)
        clean_text = re.sub(strike_pattern, r"\1", clean_text)

        # 处理链接 [text](url)
        link_pattern = r"\[([^\]]+)\]\([^)]+\)"
        for match in re.finditer(link_pattern, clean_text):
            link_content = match.group(1)
            styles["link"].append(link_content)
        clean_text = re.sub(link_pattern, r"\1", clean_text)

        # 过滤掉空的样式列表
        styles = {k: v for k, v in styles.items() if v}

    except Exception as e:
        logger.error(f"解析文本样式时出错: {e}")
        # 出错时返回原始文本和空样式
        return markdown_text, {}

    return clean_text, styles


def extract_style_patterns(text: str, style_type: str) -> List[str]:
    """
    从文本中提取特定样式类型的内容

    Args:
        text: 要解析的文本
        style_type: 样式类型 ("bold", "italic", "code", "strikethrough", "link", "math")

    Returns:
        匹配的内容列表
    """
    patterns = {
        "bold": r"\*\*([^*]+)\*\*",
        "italic": r"\*([^*]+)\*",
        "code": r"`([^`]+)`",
        "strikethrough": r"~~([^~]+)~~",
        "link": r"\[([^\]]+)\]\([^)]+\)",
        "math": r"\$(?!\s)(?P<math_text>.+?)(?!\s)\$"
    }

    if style_type not in patterns:
        logger.warning(f"不支持的样式类型: {style_type}")
        return []

    pattern = patterns[style_type]
    matches = []

    try:
        for match in re.finditer(pattern, text):
            if style_type == "math":
                matches.append(match.group("math_text"))
            else:
                matches.append(match.group(1))
    except Exception as e:
        logger.error(f"提取样式模式时出错: {e}")

    return matches


def remove_markdown_formatting(text: str) -> str:
    """
    移除文本中的所有markdown格式标记，返回纯文本

    Args:
        text: 包含markdown格式的文本

    Returns:
        移除格式后的纯文本
    """
    if not text:
        return ""

    clean_text = text

    try:
        # 按顺序移除各种格式标记
        patterns = [
            r"\$(?!\s)(?P<math_text>.+?)(?!\s)\$",  # 数学公式
            r"\*\*([^*]+)\*\*",                      # 粗体
            r"\*([^*]+)\*",                          # 斜体
            r"`([^`]+)`",                            # 代码
            r"~~([^~]+)~~",                          # 删除线
            r"\[([^\]]+)\]\([^)]+\)",                # 链接
        ]

        replacements = [
            lambda m: m.group("math_text"),  # 数学公式保留内容
            r"\1",  # 其他格式都保留内容部分
            r"\1",
            r"\1",
            r"\1",
            r"\1",
        ]

        for pattern, replacement in zip(patterns, replacements):
            clean_text = re.sub(pattern, replacement, clean_text)

    except Exception as e:
        logger.error(f"移除markdown格式时出错: {e}")
        return text

    return clean_text


def validate_markdown_syntax(text: str) -> Dict[str, List[str]]:
    """
    验证markdown语法的正确性，返回发现的问题

    Args:
        text: 要验证的markdown文本

    Returns:
        问题字典，键为问题类型，值为问题描述列表
    """
    issues = {
        "unclosed_bold": [],
        "unclosed_italic": [],
        "unclosed_code": [],
        "unclosed_strikethrough": [],
        "malformed_links": [],
        "malformed_math": []
    }

    if not text:
        return issues

    try:
        # 检查未闭合的粗体标记
        bold_count = text.count("**")
        if bold_count % 2 != 0:
            issues["unclosed_bold"].append("存在未闭合的粗体标记 **")

        # 检查未闭合的斜体标记（排除粗体中的*）
        text_without_bold = re.sub(r"\*\*[^*]*\*\*", "", text)
        italic_count = text_without_bold.count("*")
        if italic_count % 2 != 0:
            issues["unclosed_italic"].append("存在未闭合的斜体标记 *")

        # 检查未闭合的代码标记
        code_count = text.count("`")
        if code_count % 2 != 0:
            issues["unclosed_code"].append("存在未闭合的代码标记 `")

        # 检查未闭合的删除线标记
        strike_count = text.count("~~")
        if strike_count % 2 != 0:
            issues["unclosed_strikethrough"].append("存在未闭合的删除线标记 ~~")

        # 检查格式错误的链接
        link_pattern = r"\[[^\]]*\]\([^)]*\)"
        malformed_links = re.findall(r"\[[^\]]*\](?!\()|(?<!\])\([^)]*\)", text)
        if malformed_links:
            issues["malformed_links"].extend([f"格式错误的链接: {link}" for link in malformed_links])

        # 检查格式错误的数学公式
        math_count = text.count("$")
        if math_count % 2 != 0:
            issues["malformed_math"].append("存在未闭合的数学公式标记 $")

    except Exception as e:
        logger.error(f"验证markdown语法时出错: {e}")

    # 过滤掉空的问题列表
    issues = {k: v for k, v in issues.items() if v}

    return issues
