"""
Text Style Finder - 样式查找模块

负责在Mobject结构中查找带有样式信息的Text对象。
支持递归搜索和多种查找策略。
"""

from __future__ import annotations
from typing import List, Optional, Tuple
from loguru import logger
from manim import *


def find_styled_text_in_mobject(mobject: Mobject) -> Optional[Text]:
    """
    递归查找Mobject中带有样式信息的Text对象

    Args:
        mobject: 要搜索的Mobject（可能是Group、VGroup等）

    Returns:
        找到的第一个带样式信息的Text对象，如果没找到则返回None
    """
    if not mobject:
        return None

    try:
        # 如果当前对象就是带样式信息的Text对象
        if isinstance(mobject, Text) and hasattr(mobject, "text_styles"):
            return mobject

        # 如果当前对象有submobjects，递归搜索
        if hasattr(mobject, "submobjects") and mobject.submobjects:
            for submobject in mobject.submobjects:
                result = find_styled_text_in_mobject(submobject)
                if result:
                    return result

    except Exception as e:
        logger.error(f"查找带样式Text对象时出错: {e}")

    return None


def find_all_styled_text_objects(mobject: Mobject) -> List[Text]:
    """
    递归查找Mobject中的所有带样式信息的Text对象

    Args:
        mobject: 要搜索的Mobject

    Returns:
        找到的所有带样式信息的Text对象列表
    """
    styled_text_objects = []

    if not mobject:
        return styled_text_objects

    try:
        # 如果当前对象是带样式信息的Text对象
        if isinstance(mobject, Text) and hasattr(mobject, "text_styles"):
            styled_text_objects.append(mobject)

        # 如果当前对象有submobjects，递归搜索
        if hasattr(mobject, "submobjects") and mobject.submobjects:
            for submobject in mobject.submobjects:
                styled_text_objects.extend(find_all_styled_text_objects(submobject))

    except Exception as e:
        logger.error(f"查找所有带样式Text对象时出错: {e}")

    return styled_text_objects


def find_all_text_objects_in_mobject(mobject: Mobject) -> List[Text]:
    """
    递归查找Mobject中的所有Text对象（包括没有样式信息的）

    Args:
        mobject: 要搜索的Mobject

    Returns:
        找到的所有Text对象列表
    """
    text_objects = []

    if not mobject:
        return text_objects

    try:
        # 如果当前对象是Text对象
        if isinstance(mobject, Text):
            text_objects.append(mobject)

        # 如果当前对象有submobjects，递归搜索
        if hasattr(mobject, "submobjects") and mobject.submobjects:
            for submobject in mobject.submobjects:
                text_objects.extend(find_all_text_objects_in_mobject(submobject))

    except Exception as e:
        logger.error(f"查找所有Text对象时出错: {e}")

    return text_objects


def find_text_positions(text_obj: Text, substring: str) -> List[Tuple[int, int]]:
    """
    使用Text对象的_find_indexes方法查找子字符串的位置

    Args:
        text_obj: Manim Text对象
        substring: 要查找的子字符串

    Returns:
        位置列表: [(start, end), ...]
    """
    if not isinstance(text_obj, Text) or not substring:
        return []

    try:
        # 使用Text对象的内部方法_find_indexes，需要传入word和text两个参数
        return list(text_obj._find_indexes(substring, text_obj.text))
    except Exception as e:
        logger.error(f"查找文本位置失败: {e}")
        return []


def find_text_by_style_type(mobject: Mobject, style_type: str) -> List[Text]:
    """
    查找包含特定样式类型的Text对象

    Args:
        mobject: 要搜索的Mobject
        style_type: 样式类型，如 "bold", "italic"

    Returns:
        包含该样式类型的Text对象列表
    """
    matching_objects = []

    styled_objects = find_all_styled_text_objects(mobject)

    for text_obj in styled_objects:
        if hasattr(text_obj, "text_styles") and style_type in text_obj.text_styles:
            matching_objects.append(text_obj)

    return matching_objects


def find_text_by_content(mobject: Mobject, content: str, exact_match: bool = False) -> List[Text]:
    """
    根据文本内容查找Text对象

    Args:
        mobject: 要搜索的Mobject
        content: 要查找的文本内容
        exact_match: 是否精确匹配，False表示包含匹配

    Returns:
        匹配的Text对象列表
    """
    matching_objects = []

    all_text_objects = find_all_text_objects_in_mobject(mobject)

    for text_obj in all_text_objects:
        try:
            text_content = text_obj.text
            if exact_match:
                if text_content == content:
                    matching_objects.append(text_obj)
            else:
                if content in text_content:
                    matching_objects.append(text_obj)
        except Exception as e:
            logger.error(f"比较文本内容时出错: {e}")

    return matching_objects


def has_styled_text(mobject: Mobject) -> bool:
    """
    检查Mobject是否包含带样式信息的Text对象

    Args:
        mobject: 要检查的Mobject

    Returns:
        如果包含带样式信息的Text对象返回True，否则返回False
    """
    return find_styled_text_in_mobject(mobject) is not None


def count_styled_text_objects(mobject: Mobject) -> int:
    """
    统计Mobject中带样式信息的Text对象数量

    Args:
        mobject: 要统计的Mobject

    Returns:
        带样式信息的Text对象数量
    """
    return len(find_all_styled_text_objects(mobject))


def count_text_objects(mobject: Mobject) -> int:
    """
    统计Mobject中所有Text对象的数量

    Args:
        mobject: 要统计的Mobject

    Returns:
        所有Text对象的数量
    """
    return len(find_all_text_objects_in_mobject(mobject))


def get_text_hierarchy(mobject: Mobject, level: int = 0) -> List[dict]:
    """
    获取Mobject中Text对象的层次结构信息

    Args:
        mobject: 要分析的Mobject
        level: 当前层级（内部使用）

    Returns:
        层次结构信息列表，每个元素包含 {"text_obj": Text, "level": int, "has_styles": bool}
    """
    hierarchy = []

    if not mobject:
        return hierarchy

    try:
        # 如果当前对象是Text对象
        if isinstance(mobject, Text):
            hierarchy.append({
                "text_obj": mobject,
                "level": level,
                "has_styles": hasattr(mobject, "text_styles"),
                "content": getattr(mobject, "text", "")[:50] + "..." if len(getattr(mobject, "text", "")) > 50 else getattr(mobject, "text", "")
            })

        # 如果当前对象有submobjects，递归搜索
        if hasattr(mobject, "submobjects") and mobject.submobjects:
            for submobject in mobject.submobjects:
                hierarchy.extend(get_text_hierarchy(submobject, level + 1))

    except Exception as e:
        logger.error(f"获取文本层次结构时出错: {e}")

    return hierarchy


def filter_text_objects(mobject: Mobject,
                       min_length: Optional[int] = None,
                       max_length: Optional[int] = None,
                       has_styles: Optional[bool] = None,
                       style_types: Optional[List[str]] = None) -> List[Text]:
    """
    根据条件过滤Text对象

    Args:
        mobject: 要搜索的Mobject
        min_length: 最小文本长度
        max_length: 最大文本长度
        has_styles: 是否必须有样式信息
        style_types: 必须包含的样式类型列表

    Returns:
        符合条件的Text对象列表
    """
    all_text_objects = find_all_text_objects_in_mobject(mobject)
    filtered_objects = []

    for text_obj in all_text_objects:
        try:
            # 检查文本长度
            text_length = len(text_obj.text)
            if min_length is not None and text_length < min_length:
                continue
            if max_length is not None and text_length > max_length:
                continue

            # 检查是否有样式信息
            obj_has_styles = hasattr(text_obj, "text_styles")
            if has_styles is not None and obj_has_styles != has_styles:
                continue

            # 检查特定样式类型
            if style_types is not None:
                if not obj_has_styles:
                    continue
                obj_style_types = set(text_obj.text_styles.keys())
                required_types = set(style_types)
                if not required_types.issubset(obj_style_types):
                    continue

            filtered_objects.append(text_obj)

        except Exception as e:
            logger.error(f"过滤Text对象时出错: {e}")

    return filtered_objects
