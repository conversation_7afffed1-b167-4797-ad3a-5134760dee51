# vectorize_for_combo.py
import potrace
from PIL import Image
import numpy as np
import os

def vectorize_for_animation(input_image_path, output_svg_path, num_colors=24, scale_factor=4.0):
    """
    Vectorizes a color image with high resolution settings,
    optimized for a subsequent "draw and fill" animation.
    """
    if not os.path.exists(input_image_path):
        print(f"错误: 输入文件 '{input_image_path}' 不存在。")
        return

    print(f"开始高保真矢量化 '{input_image_path}'...")
    original_image = Image.open(input_image_path).convert('RGB')
    
    original_width, original_height = original_image.size
    new_width = int(original_width * scale_factor)
    new_height = int(original_height * scale_factor)
    print(f"步骤 0: 提升分辨率 (x{scale_factor}) 至 {new_width}x{new_height}...")
    hires_image = original_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    print(f"步骤 1: 将图片量化为 {num_colors} 种颜色...")
    quantized_image = hires_image.quantize(colors=num_colors, method=Image.Quantize.MEDIANCUT)
    
    palette = quantized_image.getpalette()
    palette_colors = [tuple(palette[i:i+3]) for i in range(0, len(palette), 3)]
    image_array = np.array(quantized_image)

    all_layers_paths = []

    print("步骤 2 & 3: 分离并描摹每个颜色图层...")
    for i, color in enumerate(palette_colors):
        print(f"处理color {i}...")
        if i >= num_colors: break
        mask = (image_array == i)
        bitmap = potrace.Bitmap(mask)
        path = bitmap.trace(turdsize=2)

        if path:
            hex_color = '#%02x%02x%02x' % color
            # Skip pure white as it's usually the background
            if hex_color.lower() == '#ffffff':
                continue
            all_layers_paths.append({'path_data': path, 'color': hex_color})

    print("步骤 4: 组合成最终的 SVG 文件...")
    with open(output_svg_path, "w") as fp:
        fp.write(f'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" '
                 f'width="{original_width}" height="{original_height}" viewBox="0 0 {new_width} {new_height}">')
        
        for layer in reversed(all_layers_paths):
            path_data_string = ""
            for curve in layer['path_data']:
                start_point = curve.start_point
                path_data_string += f"M{start_point[0]},{start_point[1]}"
                for segment in curve:
                    end_point = segment.end_point
                    if segment.is_corner:
                        c = segment.c
                        path_data_string += f"L{c[0]},{c[1]}L{end_point[0]},{end_point[1]}"
                    else:
                        c1 = segment.c1
                        c2 = segment.c2
                        path_data_string += f"C{c1[0]},{c1[1]} {c2[0]},{c2[1]} {end_point[0]},{end_point[1]}"
                path_data_string += "Z"
            
            fp.write(f'<path fill="{layer["color"]}" fill-rule="evenodd" d="{path_data_string}"/>')
            
        fp.write('</svg>')

    print(f"\n处理完成！已生成高保真 SVG 文件: '{output_svg_path}'")


if __name__ == '__main__':
    # 将你的新图片保存为 'source_image_2.png'
    input_file = 'source_image.jpg'
    output_file = 'combo_hires.svg'
    
    if os.path.exists(input_file):
        vectorize_for_animation(input_file, output_file, num_colors=24, scale_factor=1.0)
    else:
        print(f"请将你的图片 '{input_file}' 放置在此脚本所在的目录下。")
