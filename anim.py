# animate_draw_then_fill.py
import xml.etree.ElementTree as ET
import os

def create_draw_then_fill_animation(svg_input_path="combo_hires.svg", html_output_path="animated_draw_then_fill.html"):
    """
    Creates a two-stage animation: first the outline is drawn, then the shape is filled.
    """
    if not os.path.exists(svg_input_path):
        print(f"错误: 输入文件 '{svg_input_path}' 不存在。")
        print("请先运行 vectorize_for_combo.py 来生成它。")
        return

    try:
        namespace = {'svg': 'http://www.w3.org/2000/svg'}
        tree = ET.parse(svg_input_path)
        root = tree.getroot()
        width = root.get('width')
        height = root.get('height')
        viewBox = root.get('viewBox')
        path_elements = root.findall('svg:path', namespace)
    except ET.ParseError:
        print(f"错误: '{svg_input_path}' 不是一个有效的 SVG 文件。")
        return

    paths_data = [{'d': p.get('d'), 'color': p.get('fill', 'black')} for p in path_elements]
    viewBox_attribute = f'viewBox="{viewBox}"' if viewBox else ''

    # --- Animation constants ---
    draw_duration = "2s" # How long the drawing part takes
    fill_duration = "1s" # How long the filling part takes
    stagger_delay = 0.1 # Delay between the start of each shape's animation

    html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>SVG 描边并填充动画</title>
    <style>
        body {{ display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; background-color: #1a1a1a; color: white; }}
        svg {{ border: 1px solid #444; background-color: white; }}

        /* Animation 1: Draw the outline */
        @keyframes draw-in {{
            to {{ stroke-dashoffset: 0; }}
        }}

        /* Animation 2: Fill the shape */
        @keyframes fill-in {{
            to {{ fill-opacity: 1; }}
        }}

        .combo-path {{
            /* Chain both animations: */
            /* 1. 'draw-in' runs for {draw_duration}, holds its final state (forwards) */
            /* 2. 'fill-in' runs for {fill_duration}, but starts AFTER {draw_duration}, holds its final state */
            animation: draw-in {draw_duration} ease-out forwards, 
                       fill-in {fill_duration} ease-in {draw_duration} forwards;
        }}
    </style>
</head>
<body>
    <h1>描边 & 填充动画</h1>
    <svg width="{width}" height="{height}" {viewBox_attribute} xmlns="http://www.w3.org/2000/svg">
        {''.join([f'<path class="combo-path" d="{p["d"]}" stroke="{p["color"]}" stroke-width="1.5" fill="{p["color"]}" fill-opacity="0" fill-rule="evenodd"/>' for p in paths_data])}
    </svg>
    <script>
      document.addEventListener('DOMContentLoaded', () => {{
        const paths = document.querySelectorAll('.combo-path');
        paths.forEach((path, index) => {{
          // Setup for the 'draw-in' animation
          const length = path.getTotalLength();
          path.style.strokeDasharray = length;
          path.style.strokeDashoffset = length;
          
          // Stagger the start of the entire animation sequence for each path
          const delay = index * {{stagger_delay}};
          path.style.animationDelay = `${{delay}}s`;
        }});
      }});
    </script>
</body>
</html>
"""
    with open(html_output_path, 'w', encoding='utf-8') as f:
        f.write(html_template)
    print(f"成功创建描边并填充的动画文件: '{html_output_path}'")

if __name__ == '__main__':
    create_draw_then_fill_animation()
