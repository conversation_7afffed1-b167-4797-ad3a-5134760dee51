# 动画函数文档

## 目录

- [animate_mindmap](#animate_mindmap)
- [animate_emoji_flowchart](#animate_emoji_flowchart)
- [animate_deep_insight](#animate_deep_insight)
- [animate_competitive_analysis](#animate_competitive_analysis)
- [animate_side_by_side_comparison](#animate_side_by_side_comparison)
- [animate_timeline](#animate_timeline)
- [animate_chart](#animate_chart)
- [animate_architecture_diagram](#animate_architecture_diagram)
- [animate_step_by_step](#animate_step_by_step)
- [animate_math_step](#animate_math_step)
- [animate_counter](#animate_counter)
- [animate_markdown](#animate_markdown)
- [animate_video](#animate_video)
- [animate_qa_cards](#animate_qa_cards)
- [animate_image](#animate_image)
- [animate_highlight_content](#animate_highlight_content)
- [animate_table](#animate_table)
- [animate_text_only](#animate_text_only)

---

# animate_architecture_diagram

## 效果

在Manim场景中直接播放一个架构图动画。该架构图是使用ExcalidrawToolkit根据文本描述生成的视频。
通常用于全屏展示复杂的系统架构或流程图。


## 使用场景

- 可视化软件架构
- 展示系统组件及其交互
- 解释数据流或业务流程
- 需要通过Excalidraw风格图表进行说明的场景

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content_description | str | 用于生成Excalidraw视频的文本描述，ExcalidrawToolkit将使用此描述来创建图表内容 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| narration | str | 播放动画时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_architecture_diagram",
  "params": {
    "content_description": "一个微服务架构图，包含前端应用、API网关和多个后端服务。\n前端应用连接到API网关，网关将请求路由到用户服务、产品服务和支付服务。\n这些服务分别连接到各自的数据库。\n",
    "title": "微服务架构图",
    "narration": "这个架构图展示了我们的微服务系统结构，以及各组件之间的数据流。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_architecture_diagram",
  "params": {
    "content_description": "一个数据处理流程图，展示从数据收集到分析的完整过程。\n包含数据源、数据收集器、数据存储、处理引擎和可视化工具等组件。\n显示数据如何从原始数据转换为可行性见解。\n",
    "title": "数据处理流程图",
    "id": "data_pipeline",
    "narration": "这个数据处理流程展示了从原始数据到最终分析结果的完整路径。"
  }
}
```

## 注意事项

- ExcalidrawToolkit会根据提供的文本描述自动生成架构图视频
- 内容描述越详细，生成的架构图越准确
- 生成的视频会自动缩放以适应场景大小
- 此功能需要与外部ExcalidrawToolkit组件配合使用


---

# animate_chart

## 效果

创建并播放条形图、折线图或雷达图的动画，支持单个或多个数据集。


## 使用场景

- 可视化数据趋势和比较（折线图、条形图）
- 展示多个类别在不同指标上的表现（雷达图）
- 在视频演示中动态呈现统计数据
- 对比不同数据集之间的关系

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| chart_type | str | 图表类型。可选值：'bar'（条形图）, 'line'（折线图）, 'radar'（雷达图） | 是 | - |
| data | list[dict] | 图表数据。每个数据集为dict格式（如{"A":10,"B":20}） | 是 | - |
| narration | str | 在图表显示时播放的语音旁白文本 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| animation_style | str | 图表入场动画。可选值：'fadeIn', 'grow', 'draw', 'update', 'dynamic', bar图优先用dynamic | 否 | fadeIn |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| dataset_names | list[str] | 数据集名称列表 | 是 | - |
| x_label | str | x轴标签（条形图和折线图有效） | 否 | None |
| y_label | str | y轴标签（条形图和折线图有效） | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "bar",
    "data": [
      {
        "苹果": 75,
        "香蕉": 120,
        "橙子": 90
      }
    ],
    "title": "水果销量",
    "narration": "这是本月水果销量的条形图。",
    "x_label": "水果",
    "y_label": "销量 (千克)",
    "dataset_names": [
      "实际销量"
    ],
    "animation_style": "dynamic"
  }
}
```

### 示例 2

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "line",
    "data": [
      {
        "第一季度": 50,
        "第二季度": 65,
        "第三季度": 80,
        "第四季度": 70
      },
      {
        "第一季度": 40,
        "第二季度": 50,
        "第三季度": 60,
        "第四季度": 90
      }
    ],
    "title": "产品A vs 产品B 季度销售额",
    "dataset_names": [
      "产品A",
      "产品B"
    ],
    "narration": "此折线图比较了产品A和产品B的季度销售额。",
    "x_label": "季度",
    "y_label": "销售额 (万元)"
  }
}
```

### 示例 3

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "radar",
    "data": [
      {
        "性能": 8,
        "价格": 6,
        "外观": 9,
        "易用性": 7,
        "可靠性": 8
      },
      {
        "性能": 9,
        "价格": 4,
        "外观": 7,
        "易用性": 8,
        "可靠性": 9
      }
    ],
    "title": "产品对比",
    "dataset_names": [
      "产品X",
      "产品Y"
    ],
    "narration": "这个雷达图展示了两款产品在五个维度上的评分对比。"
  }
}
```

## 注意事项

- 对于条形图和折线图，数据键作为x轴标签，值作为y轴数据点
- 对于雷达图，数据键作为各个轴的标签，值作为该轴上的数据点
- 对于条形图，动画效果是固定的动态增长效果，animation_style参数会被忽略。


---

# animate_competitive_analysis

## 效果

创建多阶段的竞品对比分析动画，包括1v1对比、维度雷达图分析和综合评估结论。


## 使用场景

- 产品竞品分析报告展示
- 技术方案选型对比分析
- 多维度评估结果可视化
- 决策建议和结论展示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| analysis_data | dict | 竞品分析数据，包含当前方案、替代方案、维度对比分析和综合评估 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| narration | str | 整体分析过程的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_competitive_analysis",
  "params": {
    "title": "竞品对比分析",
    "analysis_data": {
      "当前方案": {
        "名称": "Quarkdown",
        "定位": "通用文档格式转换器，支持多种输入输出格式",
        "优势": "编程逻辑支持，广泛的格式支持，命令行操作方便集成",
        "劣势": "复杂排版限制",
        "适用场景": "文档转换，报告生成"
      },
      "替代方案": [
        {
          "名称": "Pandoc",
          "定位": "通用文档格式转换器，支持多种输入输出格式",
          "优势": "社区成熟度，广泛的格式支持，高度灵活",
          "劣势": "静态内容局限",
          "适用场景": "文档转换，报告生成"
        },
        {
          "名称": "Jupyter Notebook",
          "定位": "代码、文本和输出结果混合编程文档",
          "优势": "实时内核交互，强数据分析能力，结果可交互展示",
          "劣势": "排版能力受限，难以生成专业印刷书籍",
          "适用场景": "数据科学报告，研究分析"
        },
        {
          "名称": "LaTeX",
          "定位": "科学出版和技术文档领域的标准排版系统",
          "优势": "印刷级精度，排版质量极高，公式图表处理强大",
          "劣势": "学习曲线陡峭，语法复杂",
          "适用场景": "学术论文，书籍出版"
        }
      ],
      "维度对比分析": {
        "功能特性": "Quarkdown强调编程与动态内容，Pandoc重在格式转换",
        "编程能力": "Quarkdown图灵完备，Jupyter侧重数据处理",
        "输出格式": "Quarkdown/Pandoc支持多种格式",
        "易用性": "Quarkdown基于Markdown中等难度"
      },
      "综合评估": {
        "推荐指数": "4星",
        "关键结论": "Quarkdown是创新型可编程排版工具",
        "决策建议": "对于需工程化内容创作用户值得投入学习"
      }
    },
    "narration": "接下来我们将进行全面的竞品对比分析，从1v1对比开始，再展示维度雷达图，最后给出综合评估结论。"
  }
}
```

## 注意事项

- 动画分为三个主要阶段：1v1对比、雷达图分析、综合评估
- 会根据替代方案数量自动调整对比展示
- 雷达图将展示五个维度的对比分析
- 最终会给出推荐指数和决策建议
- 调用此函数会清除屏幕上的其他内容


---

# animate_counter

## 效果

在Manim场景中创建并播放计数器动画，支持数字递增/递减计数器和曲线增长图两种类型。
可以自定义起始值、目标值、标签、单位、动画时长和结束特效。


## 使用场景

- 显示随时间变化的数值，如统计数据、得分、加载进度等
- 以曲线图形式展示增长或变化趋势，并在终点显示目标值
- 强调关键性能指标(KPI)的变化

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| target_value | float | 计数器动画的目标值 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| start_value | float | 计数器的起始值。仅用于'counter'类型 | 否 | 0 |
| counter_type | str | 计数器类型。可选值：'counter'（数字计数器）, 'curve'（曲线增长图） | 否 | counter |
| label | str | 计数器的标签或标题文本 | 否 | None |
| unit | str | 计数器的单位文本。对于'counter'类型，单位显示在数字之后；对于'curve'类型，单位与目标值一起显示在曲线末端 | 否 | None |
| duration | float | 计数器动画的持续时间（秒） | 否 | 2.0 |
| effect | str | 动画结束时应用的额外视觉效果。可选值：'flash', 'zoom' | 否 | None |
| narration | str | 播放动画时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_counter",
  "params": {
    "counter_type": "counter",
    "target_value": 100,
    "title": "进度统计",
    "label": "进度",
    "unit": "%",
    "duration": 3,
    "effect": "flash",
    "narration": "加载进度达到100%。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_counter",
  "params": {
    "counter_type": "curve",
    "target_value": 500,
    "title": "用户增长",
    "label": "用户增长曲线",
    "unit": "用户",
    "duration": 4,
    "narration": "用户数量快速增长至500。"
  }
}
```

## 注意事项

- counter类型显示一个从起始值到目标值的数字动画
- curve类型显示一条增长曲线，终点显示目标值
- 结束特效在动画完成后应用，可增强视觉效果


---

# animate_deep_insight

## 效果

创建现代化的深度洞察卡片动画，采用"Sparkling Insight Card"设计风格，支持闪光特效和优雅的悬浮动画


## 使用场景

- 深度洞察分析结果展示
- 思维启发和认知增强
- 学术论文的核心观点提炼
- 商业分析的关键发现
- 创新思维的火花展示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| insights_data | list | 深度洞察数据列表，每个元素包含insight_title和insight_description字段 | 是 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| cards_per_screen | int | 每屏显示的卡片数量（建议最多3个） | 否 | 3 |
| duration_per_card | float | 每张卡片的展示时长（秒） | 否 | 4.0 |
| theme | str | 主题风格。可选值：'sparkling'（闪光）, 'elegant'（优雅）, 'modern'（现代） | 否 | sparkling |
| narration | str | 语音旁白内容 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_deep_insight",
  "params": {
    "insights_data": [
      {
        "insight_title": "用户流失的真正原因",
        "insight_description": "用户流失的真正原因，不是功能缺失，而是体验上的细微摩擦累积。"
      },
      {
        "insight_title": "数据驱动决策的核心",
        "insight_description": "有效的数据分析不在于数据量的大小，而在于找到能直接影响业务结果的关键指标。"
      },
      {
        "insight_title": "创新的本质特征",
        "insight_description": "真正的创新往往来自于对现有问题的重新定义，而不是对现有解决方案的改进。"
      }
    ],
    "title": "商业洞察精选",
    "theme": "sparkling",
    "narration": "让我们一起探索这些闪闪发光的商业洞察，每一个都可能改变我们的思维方式"
  }
}
```

### 示例 2

```json
{
  "type": "animate_deep_insight",
  "params": {
    "insights_data": [
      {
        "insight_title": "算法偏见的根源",
        "insight_description": "机器学习算法的偏见不是技术问题，而是训练数据中隐含的社会偏见的放大。"
      }
    ],
    "title": "AI伦理思考",
    "cards_per_screen": 1,
    "duration_per_card": 6.0,
    "theme": "elegant",
    "narration": "深入思考人工智能发展过程中的伦理挑战和社会责任"
  }
}
```

## 注意事项

- 完全复刻HTML版本的"Sparkling Insight Card"设计
- 柔和的浅蓝色背景营造轻松氛围
- 白色卡片背景配合圆角和阴影效果
- 金色✨图标和顶部闪光条特效
- 轻微倾斜和悬浮动画增强视觉吸引力
- 支持智能文本换行和响应式布局


---

# animate_emoji_flowchart

## 效果

创建一个交互式emoji流程图动画，支持emoji列表或包含emoji的文本输入生成对应的流程图。
支持多种布局和动画风格，自动调整大小以适应屏幕。


## 使用场景

- 展示工作流程或业务流程
- 可视化步骤序列或决策树
- 创建教学演示的流程图
- 显示系统架构或数据流

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| emoji_list | List[Tuple[str, str]] | emoji和对应关键词的列表，格式为[(emoji, keyword), ...]。与content二选一 | 否 | - |
| content | str | 包含emoji和描述的流程文本。与emoji_list二选一 | 否 | - |
| layout | str | 布局样式。可选值：horizontal, vertical, circular, grid | 否 | horizontal |
| connection_style | str | 连接线样式。可选值：arrow, line, curve | 否 | arrow |
| animation_style | str | 动画风格。可选值：sequence, simultaneous, cascade | 否 | sequence |
| max_count | int | 最大emoji数量限制（仅在使用content时有效） | 否 | 8 |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_emoji_flowchart",
  "params": {
    "title": "电商购物流程",
    "emoji_list": [
      [
        "📱",
        "打开应用"
      ],
      [
        "📚",
        "浏览商品"
      ],
      [
        "🛒",
        "选择商品"
      ],
      [
        "➕",
        "添加购物车"
      ],
      [
        "💳",
        "结算支付"
      ],
      [
        "✅",
        "订单确认"
      ]
    ],
    "layout": "horizontal",
    "connection_style": "arrow",
    "animation_style": "sequence",
    "narration": "这是一个电商购物流程的演示"
  }
}
```

### 示例 2

```json
{
  "type": "animate_emoji_flowchart",
  "params": {
    "title": "软件开发生命周期",
    "content": "需求分析 📋 收集用户需求\n设计阶段 ✏️ 制作原型设计\n开发实现 💻 编写代码\n测试验证 🔍 质量保证\n部署上线 🚀 发布产品\n",
    "layout": "circular",
    "connection_style": "curve",
    "animation_style": "cascade",
    "max_count": 6,
    "narration": "软件开发生命周期的完整流程展示"
  }
}
```

## 注意事项

- 支持直接使用emoji列表或从文本中自动提取emoji和关键词
- 布局算法会自动防止重叠并适应屏幕尺寸
- 连接线会智能避开emoji边界
- emoji列表长度会影响布局效果


---

# animate_highlight_content

## 效果

按顺序高亮一系列元素，或者对代码对象高亮特定行。


## 使用场景

- 逐步引导观众注意场景中的特定对象
- 逐行解释代码片段，高亮当前讨论的行
- 强调流程图或架构图中的特定组件序列

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| elements | list[str] | 要高亮的元素ID列表。如果指定lines参数，则此列表应只包含一个Code对象的ID | 是 | - |
| highlight_type | str | 高亮效果类型。可选值：'flash'（闪烁）, 'box'（边框）, 'underline'（下划线）, 'color'（颜色变化） | 否 | box |
| color | str | 高亮效果的颜色（十六进制或颜色名称） | 否 | #FFFF00 |
| duration_per_item | float | 每个元素或代码行组的高亮持续时间（秒） | 否 | 1.0 |
| lines | str | 要高亮的代码行范围，格式如"1-3,5,7-10"。如果提供此参数，elements应只有一个Code对象ID | 否 | None |
| narration | str | 播放动画时同步播放的语音旁白文本 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_highlight_content",
  "params": {
    "elements": [
      "text_obj1",
      "shape_obj2"
    ],
    "highlight_type": "flash",
    "color": "RED",
    "duration_per_item": 0.5,
    "narration": "首先看左边的文本，然后看右边的形状。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_highlight_content",
  "params": {
    "elements": [
      "code_block"
    ],
    "lines": "1-2,3",
    "highlight_type": "box",
    "color": "GREEN",
    "duration_per_item": 1.5,
    "narration": "现在我们来看这段代码。首先是函数定义，然后是注释。"
  }
}
```

## 注意事项

- 被高亮的元素必须已经在场景中存在并且有指定的ID
- 对于代码高亮，lines参数优先于highlight_type
- 高亮效果是暂时的，结束后元素会恢复原始状态


---

# animate_image

## 效果

在Manim场景中显示图像，支持动态入场动画和叠加注释文本。图片会从右下角斜着入场，然后顺正并进行左右移动放大。


## 使用场景

- 展示需要详细解释的图片或截图
- 展示图表、图解或可视化内容
- 显示产品或界面截图并添加叠加注释说明
- 突出显示图片中的关键要点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| image_path | str | 要显示的图片的本地文件路径 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在图片显示时播放的语音旁白文本 | 是 | - |
| annotation | str | list[str] | 作为注释叠加显示在图片上的文本 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_image",
  "params": {
    "title": "示例图片展示",
    "image_path": "assets/manim_logo.png",
    "narration": "让我们看看这张图片。",
    "annotation": "这是一张示例图片，展示了重要的内容。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_image",
  "params": {
    "title": "系统架构图",
    "image_path": "assets/manim_logo.png",
    "id": "architecture_diagram",
    "annotation": [
      "系统架构关键要点",
      "前端组件负责用户交互",
      "后端服务处理业务逻辑",
      "数据存储确保数据持久化"
    ],
    "narration": "这张架构图展示了系统的主要组件和它们之间的关系。"
  }
}
```

## 注意事项

- 图片文件必须存在且路径正确，否则会抛出FileNotFoundError
- 图片动画分为四个步骤：1）从右下角斜着入场 2）移动到中心并放大 3）旋转顺正 4）左右移动并进一步放大
- 如果提供annotation，它会以发光文字形式叠加显示在图片上，有半透明黑色背景
- annotation支持字符串或列表格式，列表中每个元素会作为一行显示
- 注释文字使用sequential动画逐行显示，提供更好的视觉效果


---

# animate_markdown

## 效果

将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。
支持各种Markdown元素，包括标题、列表、代码块、表格等。


## 使用场景

- 展示格式化的文本内容，如教程说明、演示文稿
- 在动画中展示结构化的信息，如列表和表格
- 显示带有语法高亮的代码片段
- 创建包含文本和图片的混合内容

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content | str | Markdown格式的文本内容，尽量用格式化的元素增强语义丰富性，如标题、列表、加粗、斜体等，减少纯文本的使用。文字必须简洁精练，禁止直接展示长句形式的文本，详细信息可以通过旁白体现，屏幕上只能展示最重要的关键信息。 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| title | str | 当前内容的标题，必须言简意赅，概括内容要点。 | 是 | - |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n",
    "title": "这是一个Markdown示例",
    "narration": "这是一个Markdown示例，包含标题、文本和代码。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "## 数据比较📊\n\n| 产品 | 价格 | 评分 |\n| ---- | ---- | ---- |\n| A产品 | ¥199 | 4.5分 |\n| B产品 | ¥299 | 4.8分 |\n| C产品 | ¥399 | 4.9分 |\n",
    "title": "产品价格比较",
    "narration": "这个表格比较了两款产品的价格和评分。"
  }
}
```

## 注意事项

- 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等
- 根据内容会自动调整大小以适应场景


---

# animate_math_step

## 效果

在Manim场景中专业地展示数学解题步骤，支持逐步呈现和高亮强调。支持解析markdown格式的数学公式文本，
智能识别变量、值、操作符等数学元素，并使用不同颜色和动画效果进行区分展示。


## 使用场景

- 数学定理证明的逐步展示
- 方程求解过程的详细演示
- 几何计算步骤的专业讲解
- 代数运算过程的可视化

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 解题标题，会显示在步骤上方 | 是 | - |
| steps_content | str | markdown格式的数学步骤描述，每行一个步骤，支持数学公式 | 是 | - |
| narration | str | 在步骤展示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| step_delay | float | 每步之间的延迟时间（秒） | 否 | 1.5 |
| highlight_delay | float | 高亮效果的延迟时间（秒） | 否 | 0.5 |

## DSL示例

### 示例 1

```json
{
  "type": "animate_math_step",
  "params": {
    "title": "三角函数值计算",
    "steps_content": "- `AH = x√3 = 3 + √3`\n- `BH = BD - DH = 3 - √3`\n- `tan(B) = AH / BH`\n  `= (3 + √3) / (3 - √3)`\n  `= 2 + √3`\n",
    "narration": "让我们一步步计算这个三角函数的值，首先确定AH和BH的长度，然后应用正切函数的定义。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_math_step",
  "params": {
    "title": "二次方程求解",
    "steps_content": "- `x² + 2x - 3 = 0`\n- `(x + 3)(x - 1) = 0`\n- `x = -3 或 x = 1`\n",
    "narration": "这是一个标准的二次方程求解过程，我们使用因式分解的方法来找到解。",
    "step_delay": 2.0
  }
}
```

### 示例 3

```json
{
  "type": "animate_math_step",
  "params": {
    "title": "复杂方程组求解过程",
    "steps_content": "- `2x + 3y = 12`\n- `4x - y = 8`\n- `y = 4x - 8`\n- `2x + 3(4x - 8) = 12`\n- `2x + 12x - 24 = 12`\n- `14x = 36`\n- `x = 36/14 = 18/7`\n- `y = 4(18/7) - 8`\n- `y = 72/7 - 56/7`\n- `y = 16/7`\n",
    "narration": "这是一个复杂的方程组求解过程，我们使用替换法逐步消元求解，演示分屏显示功能。",
    "step_delay": 1.2
  }
}
```

## 注意事项

- 支持标准markdown列表格式，每个`-`项为一个步骤
- 数学公式用反引号包围，会被解析为数学表达式
- 自动识别变量（字母）、值（数字）、操作符（+、-、=、/等）
- 支持分数的专业显示格式
- 每个步骤会逐步出现，带有淡入动画效果
- 重要的数学元素会有高亮强调效果
- 最终结果会有特殊的强调样式


---

# animate_mindmap

## 效果

创建交互式思维导图，支持节点聚焦、子树展开和动态布局调整。


## 使用场景

- 展示知识结构和概念层次关系
- 教学中的概念图解和思路梳理
- 项目规划和任务分解可视化
- 复杂信息的结构化展示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| data_source | object | 思维导图数据（根 + 3 级）。数据结构必须严格遵守三级层级（不含根节点）。第一级和第二级节点应作为核心标题，文字简练，且其下的子节点数量不少于3个。作为内容主体的第三级节点，则用于展示详细的关键信息，文字应相对丰富完整，每个第二级节点下的第三级节点数量应控制在3至6个之间，以确保信息的完整性和布局的美观性。 | 是 | - |
| layout_style | str | 布局样式。可选值：'balance'（平衡布局）, 'left_to_right'（从左到右）, 'right_to_left'（从右到左） | 否 | balance |
| max_depth | int | 显示的最大层级深度，超过这个深度的节点不会显示 | 否 | 3 |
| focus_sequence | list[list[str]] | focus_sequence参数可以创建引导式的节点聚焦动画，如果有需要强调的节点才用，没有可以不用。根结点无需作为第一个focus元素，如果有整体的总结性narration，可以将根节点作为最后一个focus元素。list中的元素为要聚焦的节点，包括节点文本和对应的旁白文本两个string。如果有focus_sequence元素，每个元素节点尽量配合对应的旁白，并和整体的narration要连贯，过渡流畅，不要有重复内容。 | 否 | None |
| narration | str | 在思维导图显示时播放的语音旁白文本，对思维导图进行整体介绍 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_mindmap",
  "params": {
    "title": "人工智能知识体系",
    "data_source": "{\n  \"标题\": \"人工智能\",\n  \"子章节\": [\n    {\n      \"标题\": \"机器学习\",\n      \"子章节\": [\n        {\"标题\": \"监督学习\"},\n        {\"标题\": \"无监督学习\"},\n        {\"标题\": \"强化学习\"}\n      ]\n    },\n    {\n      \"标题\": \"深度学习\",\n      \"子章节\": [\n        {\"标题\": \"神经网络\"},\n        {\"标题\": \"卷积网络\"},\n        {\"标题\": \"循环网络\"}\n      ]\n    }\n  ]\n}\n",
    "layout_style": "balance",
    "max_depth": 3,
    "id": "AI_mindmap",
    "focus_sequence": [
      [
        "人工智能",
        "让我们从人工智能开始"
      ],
      [
        "机器学习",
        "然后是机器学习"
      ],
      [
        "深度学习",
        "最后是深度学习"
      ]
    ],
    "narration": "让我们通过这个思维导图来了解人工智能的主要分支和技术体系。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_mindmap",
  "params": {
    "title": "机器学习深入分析",
    "id": "AI_mindmap",
    "focus_sequence": [
      [
        "机器学习",
        "让我们从机器学习开始"
      ],
      [
        "监督学习",
        "然后是监督学习"
      ],
      [
        "无监督学习",
        "最后是无监督学习"
      ]
    ],
    "narration": "让我们更深入地了解一下机器学习的两个主要分支，监督学习和无监督学习。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_mindmap",
  "params": {
    "title": "项目结构图",
    "data_source": "assets/mindmap_data.json",
    "layout_style": "left_to_right",
    "max_depth": 2,
    "narration": "这是一个从文件加载的思维导图结构。"
  }
}
```


---

# animate_qa_cards

## 效果

创建完全按照现代设计风格的QA卡片展示动画


## 使用场景

- 教育培训中的问答展示
- 知识总结和回顾
- 产品FAQ展示
- 学术论文要点问答
- 面试问题练习

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| qa_data | list | QA数据列表，每个元素包含question和answer字段 | 是 | - |
| title | str | 展示标题 | 是 | - |
| cards_per_screen | int | 每屏显示的卡片数量（建议最多3个） | 否 | 3 |
| duration_per_card | float | 每张卡片的展示时长（秒） | 否 | 2.0 |
| narration | str | 语音旁白内容 | 是 | - |

## DSL示例

### 示例 1

```json
{
  "type": "animate_qa_cards",
  "params": {
    "qa_data": [
      {
        "question": "什么是'第一性原理'思维？",
        "answer": "第一性原理是一种解决问题的思维方式，它强调回归事物的本质和基本公理进行分析，而不是依赖类比或既有经验。"
      },
      {
        "question": "什么是'SMART原则'？",
        "answer": "SMART原则是设定目标的经典方法论，确保目标清晰可行。它代表：具体的、可衡量的、可达成的、相关的和有时限的。"
      },
      {
        "question": "什么是'心流'状态？",
        "answer": "心流是一种个体完全沉浸、全神贯注于某项活动时的心理状态。在这种状态下，人会感到极大的愉悦和满足。"
      }
    ],
    "title": "知识问答卡片",
    "narration": "让我们通过这些精美的卡片来学习重要概念"
  }
}
```

## 注意事项

- 问题要简短，有洞察力
- 回答言简意赅


---

# animate_side_by_side_comparison

## 效果

创建左右两栏并排比较的布局，可以比较不同类型的内容（文本、代码、图像等）。


## 使用场景

- 对比两种不同的代码实现或算法
- 比较"之前"与"之后"的状态
- 并列展示问题和解决方案
- 对比两个图像、图表或设计

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 当前内容的标题，会显示在内容上方 | 是 | - |
| left_content | str | 左侧内容（文本、代码字符串或图像路径） | 是 | - |
| left_type | str | 左侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown' | 是 | - |
| right_content | str | 右侧内容（文本、代码字符串或图像路径） | 是 | - |
| right_type | str | 右侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown' | 是 | - |
| left_title | str | 左侧窗格的标题 | 否 | None |
| right_title | str | 右侧窗格的标题 | 否 | None |
| transition | str | 内容入场的动画效果。可选值：'fadeIn', 'slideUp', 'none' | 否 | fadeIn |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "title": "斐波那契数列的递归和迭代实现",
    "left_content": "def fib_recursive(n):\n    if n <= 1:\n        return n\n    return fib_recursive(n-1) + fib_recursive(n-2)\n",
    "left_type": "code",
    "left_title": "递归斐波那契",
    "right_content": "def fib_iterative(n):\n    a, b = 0, 1\n    for _ in range(n):\n        a, b = b, a + b\n    return a\n",
    "right_type": "code",
    "right_title": "迭代斐波那契",
    "narration": "让我们比较斐波那契数列的递归和迭代实现。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "title": "Python 和 Java 的信息对比",
    "left_content": "{\n    \"name\": \"Python\",\n    \"year\": 1991,\n    \"creator\": \"Guido van Rossum\",\n    \"paradigms\": [\"面向对象\", \"命令式\", \"函数式\"]\n}\n",
    "left_type": "json",
    "left_title": "Python 信息",
    "right_content": "{\n    \"name\": \"Java\",\n    \"year\": 1995,\n    \"creator\": \"James Gosling\",\n    \"paradigms\": [\"面向对象\", \"命令式\"]\n}\n",
    "right_type": "json",
    "right_title": "Java 信息",
    "transition": "fadeIn",
    "narration": "Python 和 Java 的信息对比。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "title": "Python 和 Java 的信息对比",
    "left_content": "# Python\n\n- 创建于1991年\n\n- 由Guido van Rossum开发\n\n- 支持面向对象、命令式和函数式编程\n",
    "left_type": "markdown",
    "left_title": "Python 信息",
    "right_content": "# Java\n\n- 创建于1995年\n\n- 由James Gosling开发\n\n- 主要支持面向对象编程\n",
    "right_type": "markdown",
    "right_title": "Java 信息",
    "transition": "fadeIn",
    "narration": "Python 和 Java 的信息对比。"
  }
}
```

## 注意事项

- 对于'image'类型，content应为图像文件的本地路径
- 对于'code'和'json'类型，会自动应用语法高亮
- 内容会自动缩放以适应各自的窗格大小
- 调用此函数会清除屏幕上的其他内容


---

# animate_step_by_step

## 效果

在Manim场景中创建并播放一个分步骤讲解的动画。
左侧展示步骤节点，每一步包含序号和操作描述，节点动态生成并向上移动。
右侧展示每一步的具体内容（markdown格式），支持代码、列表、文本等。
最后所有步骤节点缩放移动到画面正中，展示整体概念。


## 使用场景

- 教学演示中的分步骤讲解，如算法步骤、操作流程等
- 产品功能介绍，逐步展示各个功能点
- 项目开发流程演示，突出每个阶段的重点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| steps | array | 步骤列表，每个元素是一个dict，包括 step_number, title, content, color（可选）, narration（可选） 字段 | 是 | - |
| intro_narration | str | 开场介绍语音旁白文本 | 是 | - |
| outro_narration | str | 结尾总结语音旁白文本 | 是 | - |
| title | str | 整体标题 | 是 | - |
| subtitle | str | 副标题 | 否 | None |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_step_by_step",
  "params": {
    "steps": [
      {
        "step_number": "1",
        "title": "初始化数据",
        "content": "## 创建数组\n```python\narr = [64, 34, 25, 12, 22, 11, 90]\n```\n- 准备待排序的数组\n- 记录数组长度\n",
        "color": "#FF6B6B",
        "narration": "首先我们初始化一个待排序的数组"
      },
      {
        "step_number": "2",
        "title": "选择最小元素",
        "content": "## 查找最小值\n```python\nmin_idx = 0\nfor i in range(1, len(arr)):\n    if arr[i] < arr[min_idx]:\n        min_idx = i\n```\n- 遍历未排序部分\n- 找到最小元素的索引\n",
        "color": "#4ECDC4",
        "narration": "接下来在未排序部分找到最小的元素"
      },
      {
        "step_number": "3",
        "title": "交换元素",
        "content": "## 元素交换\n```python\narr[0], arr[min_idx] = arr[min_idx], arr[0]\n```\n- 将最小元素移到已排序部分的末尾\n- 扩大已排序区域\n",
        "color": "#45B7D1",
        "narration": "然后将最小元素与第一个位置交换"
      }
    ],
    "title": "选择排序算法演示",
    "subtitle": "逐步理解排序过程",
    "intro_narration": "今天我们来学习选择排序算法的工作原理",
    "outro_narration": "通过这三个步骤，我们完成了选择排序的一轮操作"
  }
}
```

## 注意事项

- 步骤按数组中的顺序呈现，每个步骤的内容支持完整的markdown语法
- 左侧节点会动态生成并向上移动，右侧内容会淡出后显示新内容
- 可以为每个步骤指定颜色，或使用默认颜色方案
- 最后所有步骤节点会缩放移动到画面中央，形成整体概览
- 与timeline的差别是，step_by_step需要讲解每个步骤中的详细内容，因此更适合例子讲解等场景，而timeline只是展示事件的整体脉络
- title, subtitle与step title需要精简，不能字数太多，否则会导致文字重叠等问题


---

# animate_table

## 效果

在Manim场景中创建并展示表格，支持逐行高亮显示效果。表格包含表头和数据行，单元格之间有间隙，支持黄色高亮状态。


## 使用场景

- 展示统计数据表格
- 显示对比数据分析
- 逐步突出重要数据行
- 展示结构化信息

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| headers | List[str] | 表格表头列表 | 是 | - |
| data | List[List[str]] | 表格数据，每个子列表代表一行数据 | 是 | - |
| title | str | 表格标题 | 是 | - |
| highlight_rows | List[int] | 需要高亮的行索引列表（从0开始，不包括表头） | 否 | [] |
| cell_spacing | float | 单元格之间的间距 | 否 | 0.2 |
| highlight_color | str | 高亮颜色，支持Manim颜色常量 | 否 | YELLOW |
| narration | str | 在表格展示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_table",
  "params": {
    "title": "城市职业变化统计表",
    "headers": [
      "城市",
      "4月入职",
      "5月离职",
      "5月入职",
      "5月离职",
      "6月入职",
      "6月离职",
      "主要离职原因"
    ],
    "data": [
      [
        "北京",
        "23",
        "11",
        "15",
        "7",
        "17",
        "3",
        "家庭原因"
      ],
      [
        "上海",
        "43",
        "2",
        "12",
        "8",
        "19",
        "11",
        "薪资原因"
      ],
      [
        "深圳",
        "11",
        "3",
        "15",
        "2",
        "16",
        "5",
        "个人发展"
      ],
      [
        "杭州",
        "15",
        "1",
        "19",
        "3",
        "15",
        "0",
        "家庭原因"
      ],
      [
        "武汉",
        "17",
        "0",
        "9",
        "7",
        "7",
        "8",
        "晋升"
      ],
      [
        "广州",
        "9",
        "1",
        "3",
        "0",
        "2",
        "2",
        "个人问题"
      ],
      [
        "合计",
        "118",
        "18",
        "73",
        "27",
        "76",
        "29",
        "/"
      ]
    ],
    "highlight_rows": [
      1
    ],
    "narration": "让我们来看看这个城市职业变化的统计表格。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_table",
  "params": {
    "title": "产品销量对比分析表",
    "headers": [
      "产品",
      "Q1销量",
      "Q2销量",
      "增长率"
    ],
    "data": [
      [
        "产品A",
        "100",
        "120",
        "20%"
      ],
      [
        "产品B",
        "80",
        "95",
        "18.8%"
      ],
      [
        "产品C",
        "60",
        "90",
        "50%"
      ]
    ],
    "highlight_rows": [
      0,
      2
    ],
    "narration": "这是产品销量的对比分析表格。"
  }
}
```

## 注意事项

- 表格内容不要超过8个字符
- 表格会自动调整尺寸以适应屏幕
- 高亮行会以黄色背景显示
- 单元格之间的间距可以调整
- 支持中文内容显示
- 表格展示后会逐行进行高亮动画


---

# animate_text_only

## 效果

创建纯文本内容的动态展示动画，专门用于文字信息的呈现。支持清单、要点、步骤等文本内容的动态显示，
包含渐变背景、发光字体效果、逐字显示动画等视觉特效，适合纯文本内容的精美展示。


## 使用场景

- 展示要点清单、任务列表、检查清单等文本内容
- 创建纯文字的动态内容展示，如学习要点、工作计划等
- 制作带有视觉特效的文本演示，突出重要信息
- 文字内容的分步展示和强调

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| title | str | 文本展示的主标题 | 是 | - |
| items | List[Dict] | 文本项目列表，每个项目包含text、tags字段，颜色会自动分配 | 否 | None |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_text_only",
  "params": {
    "title": "📚 学习要点总结",
    "items": [
      {
        "text": "理论知识学习",
        "tags": "#基础概念 #核心原理"
      },
      {
        "text": "实践操作练习",
        "tags": "#动手实践 #技能提升"
      },
      {
        "text": "目标达成检验",
        "tags": "#效果评估 #持续改进"
      }
    ],
    "narration": "这是我们学习过程中需要重点关注的三个要点。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_text_only",
  "params": {
    "title": "🎯 工作计划清单",
    "items": [
      {
        "text": "制定详细计划",
        "tags": "#时间安排 #任务分解"
      },
      {
        "text": "高效执行任务",
        "tags": "#专注投入 #质量保证"
      },
      {
        "text": "跟踪进度反馈",
        "tags": "#进度监控 #及时调整"
      },
      {
        "text": "完成总结复盘",
        "tags": "#成果总结 #经验提炼"
      }
    ],
    "narration": "高效工作需要遵循这四个关键步骤。"
  }
}
```

## 注意事项

- 专门用于纯文本内容的动态展示，不涉及图片或视频
- 支持最多5个文本项目的展示
- 每个文本项包含主文本、标签，颜色自动分配
- 具有发光字体效果和逐字显示动画
- 适合要点总结、清单展示、步骤说明等文本场景
- 使用渐变背景和动态特效提升文本展示的视觉效果


---

# animate_timeline

## 效果

在Manim场景中创建并播放一个动态的、分段构建的时间轴动画。
每个事件包含时间点（年份）、标题、描述文本，并可选择性地包含图片和自定义颜色。
事件详情会交替显示在时间轴的上方和下方。


## 使用场景

- 展示项目里程碑或历史事件序列，具有更强的视觉吸引力
- 解释一个过程的各个阶段，每个阶段有清晰的标题和描述
- 可视化产品发布路线图，突出显示关键节点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| events | list[EventData | dict[str, Any]] | 时间轴事件列表。每个元素可以是EventData对象或字典，包含year(时间点), title(标题), description(描述), emoji(表情), color(颜色), narration(事件旁白)等属性，其中narration会在当前时间轴事件显示的同时作为旁白播放。如果有全局的content_narration，那么事件的narration将被忽略。 | 是 | - |
| intro_narration | str | 时间轴动画开始时播放的语音旁白文本（会配合开始标题的显示动画一起播放） | 否 | None |
| outro_narration | str | 时间轴动画结束时播放的语音旁白文本（会配合最后整个时间轴缩放动画一起播放） | 否 | None |
| content_narration | str | 时间轴动画播放时同步播放的语音旁白文本（与每个事件的narration互斥） | 是 | - |
| title | str | 时间轴标题 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_timeline",
  "params": {
    "events": [
      {
        "year": "1950",
        "title": "The Beginning",
        "description": "A new era starts.",
        "emoji": "🌍",
        "color": "#F0E68C",
        "narration": "A new era starts."
      },
      {
        "year": "1965",
        "title": "Major Discovery",
        "description": "Key findings published.",
        "emoji": "🌞",
        "color": "#FFA500",
        "narration": "Key findings published."
      },
      {
        "year": "1980",
        "title": "Expansion",
        "description": "Growth and development.",
        "emoji": "🌛",
        "color": "#B22222",
        "narration": "Growth and development."
      },
      {
        "year": "2000",
        "title": "New Century",
        "description": "Looking ahead.",
        "emoji": "🪵",
        "color": "#FFC0CB",
        "narration": "Looking ahead."
      },
      {
        "year": "2020",
        "title": "Modern Times",
        "description": "Current state of affairs.",
        "emoji": "🚀",
        "color": "#9370DB",
        "narration": "Current state of affairs."
      }
    ],
    "title": "Historical Timeline",
    "intro_narration": "A journey through time, highlighting key moments.",
    "outro_narration": "This is the end of the timeline."
  }
}
```

## 注意事项

- 事件列表按数组中的顺序呈现，而非按年份排序
- 时间轴展示时会先显示主轴，然后顺序显示每个事件
- 可以为每个事件指定颜色，或使用默认颜色方案
- emoji属性会尝试下载并显示对应图标，如果失败则仅显示简单节点
- 如果时间轴节点较多（超过5个），每个节点单独的narration会使视频变得冗长，可以设置简洁的整体content_narration来介绍时间轴的背景信息


---

# animate_video

## 效果

在Manim场景中播放视频文件，支持可选的文本叠加和语音旁白。


## 使用场景

- 在教程或演示中展示屏幕录制或外部视频片段
- 播放动画片段作为更复杂场景的一部分
- 叠加解释性文本或字幕到视频内容上
- 配合旁白同步视频演示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| video_path | str | 要播放的视频文件的本地路径 | 是 | - |
| title | str | 视频标题 | 否 | None |
| overlay_text | str | 可选的叠加文本，显示在视频之上。多行文本用"\n"分隔 | 否 | None |
| overlay_animation_delay | float | 每行文本动画之间的延迟（秒） | 否 | 1.0 |
| narration | str | 在视频播放时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_video",
  "params": {
    "video_path": "assets/demo.mp4",
    "narration": "这是一个演示视频。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_video",
  "params": {
    "video_path": "assets/demo.mp4",
    "overlay_text": "重要提示！\n请注意这个关键点",
    "overlay_animation_delay": 0.5,
    "narration": "请注意视频中的这个重点部分。"
  }
}
```

## 注意事项

- 视频文件必须存在且路径正确，否则函数会记录错误并返回
- 视频会自动缩放以适应场景
- 叠加文本会依次显示，每行之间有指定的延迟


---