# Feynman Workflow Configuration
# Model Configuration
model:
  platform: "openrouter"
  #type: "google/gemini-2.5-flash-preview-05-20"
  #type: "google/gemini-2.5-flash"
  #type: "gemini-2.5-pro"
  #type: "gemini-2.5-flash"
  #type: "google/gemini-2.5-pro-preview"
  # type: "google/gemini-2.5-flash-lite-preview-06-17"
  # type: "google-gemini2.5pro-preview-06-05"
  type: "google/gemini-2.5-pro"
  #type: "openai/gpt-4.1"
  #temperature: 0.7
  max_tokens: 120000  # 推荐范围: 1024-32768, 避免使用过大的值如999_999_999
  # API配置
  api:
    openai_compatibility_api_base_url: "https://openrouter.ai/api/v1"
    openai_compatibility_api_key: "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"
    openrouter_api_base_url: "https://openrouter.ai/api/v1"
    openrouter_api_key: "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"
    use_cheap_model: false
    cheap_api_base_url: "https://jeniya.cn/v1"
    cheap_api_key: "sk-t0rhL0dZAhZdFqY1OPR5vKxo2NpQURwQB10nEdFHqDOWMTYl"  # Gemini 系列专用: gemini-2.5-pro, gemini-2.5-flash, gemini-2.5-flash-lite-preview-06-17
    #cheap_api_key: "sk-k2GsBDwwE1JREL45OiSZP31A7Io481ZhMBB55dJoktYCYXo1"  # Gemini 系列专用: gemini-2.5-pro, gemini-2.5-flash, gemini-2.5-flash-lite-preview-06-17
    #cheap_api_key: "sk-JpXbwzhmQEGQGNMTwo9fsTQVjD7BwxEi7tu4z0RWAwOcyq54"  # 其他模型: gpt-4.1-2025-04-14, claude-sonnet-4-20250514

# Example Judge Model Configuration
example_judge_model:
  type: "anthropic/claude-sonnet-4"  # 使用更强的模型进行评估

vision_storyboard_judge_model:
  type: "anthropic/claude-sonnet-4"  # 使用更强的模型进行评估

# Intent Classification Model Configuration
intent_classification_model:
  type: "google/gemini-2.5-flash-lite-preview-06-17"  # 意图分类专用模型

# File Configuration
files:
  outline_file: "output/outline.json"
  storyboard_file: "output/storyboard.json"
  content_file: "output/paper_content.json"
  paper_discussion_file: "output/paper_discussion.json"
  feynman_explanation_file: "output/feynman_explanation.json"
  feynman_full_text_file: "output/feynman_explanation_full.txt"
  output_dir: "output"

# Material Agent Configuration - 由意图分类Agent动态设置
material:
  # 简化输入配置（意图分类Agent专用）
  intent_input:
    # URL输入 - GitHub项目、ArXiv论文、普通网页等
    url: "https://arxiv.org/pdf/2508.14040"
    #url : ""
    # 文件输入 - 各种文档、图片等格式
    file: ""
    # 聊天指令 - 知识性询问或学习需求
    #chat: "冒泡排序算法解释"
    chat: "为研究人员介绍这篇论文，目的是介绍工作价值，包括核心能力、关键概念、方案对比、实验效果等，风格具备批判性，讲解具备深入思考而不停留在表面信息"
    #chat: "分析这个博客内容，为读者提供核心信息的讲解"
    #chat: "给技术爱好者介绍这个AI项目，目的介绍项目价值、具体核心能力等，风格是应用性，吸引用户关注试用"
    #chat: "针对这个题目，给出详细的解题过程和知识点分析"
  pdf:
    max_num_pages: 40
  # 素材生成配置
  max_rounds: 1
  output_dir: "output"
  material_file: "output/video_material.md"
  generator_enabled: true  # 控制素材生成器是否启用
  analysis_techniques:
    - "audience_focus"
    - "content_refinement"
    - "style_adaptation"
    - "multimedia_handling"
  # 默认视频长度
  default_video_length: "2分钟"
  # 默认目的描述
  default_purpose: "提供简单易懂的内容分析，面向一般受众，以客观中立的风格呈现"
  # 目标受众群体预设
  audience_presets:
    research: "研究人员"
    student: "学生"
    tech_enthusiast: "技术爱好者"
    general: "一般受众"
  # 风格预设
  style_presets:
    academic: "学术严谨"
    casual: "通俗易懂"
    objective: "客观中立"
    enthusiastic: "热情活力"

  # 素材扩充配置
  material_enhance:
    screen_record: false
    timeline_generation: true
    six_dimensions_evaluation: false
    deep_insight_qa: true  # 深度洞察问答工具
    table_generation: true  # 表格生成工具
    example_explain: false  # 举例说明工具
    deep_insight: false  # 深度洞察分析工具
    core_info_extraction: false  # 核心信息提取工具
    mindmap_generation: true  # 思维导图生成工具
    competitive_analysis: false  # 竞品对比分析工具
    architecture_diagram: false  # 架构图生成工具
    mermaid_diagram: false  # Mermaid图表生成工具
    chart_generation: true  # 图表生成工具
    emoji_flowchart: false  # Emoji流程图工具

# 动画函数开关控制
animate_functions:
  animate_timeline: true
  animate_mindmap: true
  animate_counter: true
  animate_emoji_flowchart: false
  animate_deep_insight: false
  animate_highlight_content: false
  animate_step_by_step: false
  animate_qa_cards: true
  animate_table: true
  animate_side_by_side_comparison: false
  animate_architecture_diagram: false
  animate_competitive_analysis: false
  animate_markdown: true
  animate_image: true
  animate_text_only: false
  animate_chart: true
  animate_video: false

# Agent Configuration
agents:
  source: False
  outline_generator: True
  outline_reflector: True
  storyboard_generator: True
  storyboard_reflector: True
  content_generator: False
  material_generator: True

# 大纲生成配置
outline:
  max_rounds: 3
  query_template: "我想了解这篇文章: {url}"
  user_profile:
    education_level: "研究生"
    field: "计算机科学"
    interests:
      - "人工智能"
      - "机器学习"
      - "自然语言处理"
    expertise_level: "中级"
    content_preferences:
      style: "通俗易懂"
      depth: "中等"
      format: "视频讲解"
  content_topics:
    - "论文解读"
    - "学术分享"
    - "技术讲解"

# Feynman Agent Configuration
feynman:
  article_file: "output/paper_content.md"
  storyboard_file: "output/storyboard.json"
  max_rounds: 3
  explanation_techniques:
    - "simple_language"
    - "daily_analogy"
    - "code_examples"
    - "data_analysis"
    - "interactive_exercises"
    - "metaphors"
    - "visualization"
  output_format: "json"

# 例子解释代理配置 - 由意图分类Agent动态设置

# 视频分析配置
video_analysis:
  # 功能开关
  content_analysis: true          # 功能1: 客观详细分析视频内容和逻辑
  aesthetics_evaluation: true     # 功能2: 视频视觉评估功能
  highlight_analysis: true        # 功能3: 视频亮点分析和复刻建议
  manim_optimization: true        # 功能4: Manim视频优化建议

  # 视频帧提取配置
  frame_extraction:
    max_frames: 10                # 最大提取帧数
    interval_seconds: 5           # 提取间隔（秒）

  # 输出配置
  output_dir: "output"            # 输出目录
  save_frames: false              # 是否保存提取的帧图片

# 工作流控制配置
workflow:
  # 启用或禁用大纲生成模块
  enable_outline: true
  # 启用或禁用费曼解释模块
  enable_feynman: true
  # 启用或禁用素材规范化模块
  enable_material: true
  # 最大修改尝试次数
  max_revision_attempts: 2
  # 各阶段结果文件路径
  intention_file: "output/intention_result.json"
  paper_info_file: "output/paper_info.json"
  paper_content_file: "output/paper_content.md"
  outline_file: "output/outline_result.json"
  discussion_file: "output/discussion_result.json"
  feynman_file: "output/feynman_result.json"
  material_file: "output/material_result.json"
  generation_file: "output/generated_content.json"
  media_fix_file: "output/fixed_content.json"
  revision_file: "output/revised_content.json"
  max_fix_attempts: 3
  chroma_db_path: "data/rag/chroma_db"
  manim_docs_path: "data/rag/manim_docs"
  embedding_model: "nomic-embed-text"

  # 工作流起始阶段 (可选)
  start_stage: null  # 可选值: scene_plan, vision_storyboard, technical_implementation, animation_narration, code_generation, rendering, evaluation

  # 启用功能开关
  enable_rag: true
  enable_context_learning: true  # 启用上下文学习
  enable_visual_fix: true  # 启用视觉修复

  # 各个关键步骤的开关控制
  step_switches:
    enable_scene_plan: true  # 启用场景规划步骤
    enable_vision_storyboard: true  # 启用视觉故事板步骤
    enable_technical_implementation: false  # 启用技术实现步骤
    enable_animation_narration: false  # 启用动画叙述步骤
    enable_code_generation: true  # 启用代码生成步骤
    enable_rendering: true  # 启用视频渲染步骤
    enable_evaluation: false  # 启用视频评估步骤
    enable_video_combine: true  # 启用视频合并步骤

  # 各阶段输出文件路径配置
  scene_plan_file: "output/{topic}/scene_outline.txt"
  vision_storyboard_dir: "output/{topic}/vision_storyboard"
  technical_implementation_dir: "output/{topic}/technical_implementation"
  animation_narration_dir: "output/{topic}/animation_narration"
  code_dir: "output/{topic}/code"
  video_dir: "output/{topic}/videos"
  evaluation_file: "output/{topic}/evaluation.json"
  code_agent:
    use_cheap_model: true
    max_iteration_per_step: 20
    enable_sequential_thinking: true
    enable_get_docs: true
    agent_framework: smolagents
    model: "google/gemini-2.5-pro"
    memory_model: "google/gemini-2.5-flash-lite-preview-06-17"
    summary_model: "google/gemini-2.5-flash-lite-preview-06-17"



# Manim Configuration
manim:
  quality: "h"
  width: 1920
  height: 1080
  background_color: "#000000"

# Background Configuration
background:
  type: "gray"  # Options: "gray", "hyperbolic_network"




# 全局配置
app:
  name: "Feynman-AI"
  version: "0.2.0"
  environment: "development"
  server: false  # 控制是否使用服务器模式路径设置，默认false

# API配置
api:
  openai:
    api_key: ${OPENAI_API_KEY}
    base_url: ${OPENAI_API_BASE}
    organization: ${OPENAI_ORG_ID}
    default_model: "gpt-4-turbo-preview"
    max_retries: 3
    timeout: 60

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  handlers:
    - "console"
    - "file"
  file_handler:
    filename: "logs/app.log"
    max_bytes: 10485760  # 10MB
    backup_count: 5

code_generation:
  repo_url: "https://github.com/camel-ai"
  repo_path: "/Users/<USER>/Documents/git/camel"
  prompt: "写一个agent收集近一周的经济事件并分析对美股和A股的影响，他通过openBB工具获取相关数据。"
  reference_files: ["output/camels/code_refer.py", "output/camels/openBB_help.txt"]
  output_md: "output/camels/camel_compare_code_info.md"
  output_py: "output/camels/camel_compare_code.py"
  video_file: "output/camels/camel_compare_video.mp4"

transition:
  enable: true
  type: "transformation"
  run_time: 1.0
  state_dir: "output/scene_states"

video:
  max_workers: 6
  quality: "h"
  # Video production settings
  duration: "3min"  # Default video duration (e.g., "2min", "5min", "10min")
  content_language: "Chinese"  # Content language (e.g., "Chinese", "English")

theme: professional

# Speech Configuration (TTS Settings)
# zh-CN-YunxiNeural、zh-CN-XiaoxiaoNeural、zh-CN-XiaoyiNeural、zh-CN-YunjianNeural、zh-CN-YunxiaNeura
# zh-CN-YunyangNeural、zh-CN-liaoning-XiaobeiNeural、zh-CN-shaanxi-XiaoniNeural、zh-HK-HiuGaaiNeural、zh-HK-HiuMaanNeural
# zh-HK-WanLungNeural、zh-TW-HsiaoChenNeural、zh-TW-HsiaoYuNeural、zh-TW-YunJheNeural
speech:
  global_speed: 1.2
  voice: "zh-CN-YunxiNeural"
  service: "edgetts"

# Background Music Configuration
background_music:
  enabled: true  # 是否启用背景音乐
  audio_file: "assets/bgm_v2.m4a"  # 背景音乐文件路径
  volume: 0.05  # 背景音乐音量 (0.0-1.0)
  fade_in: 2.0  # 淡入时间(秒)
  fade_out: 3.0  # 淡出时间(秒)
  loop: true  # 是否循环播放
  start_offset: 0.0  # 音乐开始偏移时间(秒)



