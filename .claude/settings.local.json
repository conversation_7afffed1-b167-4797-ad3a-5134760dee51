{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(manim:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./run_simple_news.sh:*)", "Bash(ruff check:*)", "Bash(ruff format:*)", "Bash(PYTHONPATH=/Users/<USER>/Documents/codes/agentic-feynman manim -pql output/SelfAttention算法/self_attention_animation.py SelfAttentionAnimation)", "Bash(pip install:*)", "Bash(PYTHONPATH=. manim -pql output/SelfAttention算法/self_attention_animation.py SelfAttentionAnimation)", "<PERSON><PERSON>(pip uninstall:*)"], "deny": []}}