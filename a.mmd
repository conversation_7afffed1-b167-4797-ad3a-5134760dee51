flowchart TD
    Start([开始]) --> A[选择一个待办任务]
    A --> B[设定番茄钟: 25分钟]
    B --> C[专注工作<br>期间拒绝所有干扰]
    C --> D[铃响: 休息5分钟]
    D --> E{完成4个番茄钟?}
    
    E -- 否 --> A
    E -- 是 --> F[进行长休息: 15-30分钟]
    F --> A

    C --> G[遇到中断?]
    G -- 是 --> H{中断类型?}
    H -- 外部中断<br>(他人打扰) --> I[礼貌告知<br>稍后处理<br>并记下它]
    I --> J[继续当前番茄钟]
    H -- 内部中断<br>(自身想法) --> K[将想法记下<br>然后立即忽略]
    K --> J
    
    J --> C

    style Start fill:#f9f
    style C fill:#fcf
    style D fill:#dfd
    style F fill:#cdf
