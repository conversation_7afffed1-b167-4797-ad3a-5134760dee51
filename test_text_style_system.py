#!/usr/bin/env python3
"""
Text Style System 测试文件

测试text_style_system各模块的功能，验证系统的正确性。
这个测试文件不依赖Manim，可以独立运行基础功能测试。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dsl.v2.utils.text_style_system.parser import (
    parse_text_styles, 
    extract_style_patterns, 
    remove_markdown_formatting,
    validate_markdown_syntax
)

from dsl.v2.utils.text_style_system.interface import (
    extract_text_styles,
    get_clean_text,
    TextStyleProcessor
)


def test_parser_module():
    """测试parser模块的功能"""
    print("=" * 50)
    print("测试 Parser 模块")
    print("=" * 50)
    
    # 测试基本解析功能
    test_cases = [
        "这是**加粗**和*斜体*的文本",
        "包含`代码`和~~删除线~~的内容",
        "数学公式 $E=mc^2$ 和链接 [百度](https://baidu.com)",
        "复杂文本：**粗体**、*斜体*、`代码`、~~删除~~、$x^2$",
        "没有任何格式的普通文本",
        "",  # 空文本
        "**未闭合的粗体",  # 格式错误
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: '{text}'")
        try:
            clean_text, styles = parse_text_styles(text)
            print(f"  纯文本: '{clean_text}'")
            print(f"  样式信息: {styles}")
            
            # 测试单独提取样式
            if styles:
                for style_type in styles:
                    patterns = extract_style_patterns(text, style_type)
                    print(f"  {style_type} 模式: {patterns}")
            
            # 测试移除格式
            clean_only = remove_markdown_formatting(text)
            print(f"  仅移除格式: '{clean_only}'")
            
            # 验证语法
            issues = validate_markdown_syntax(text)
            if issues:
                print(f"  语法问题: {issues}")
            
        except Exception as e:
            print(f"  错误: {e}")


def test_interface_module():
    """测试interface模块的功能"""
    print("\n" + "=" * 50)
    print("测试 Interface 模块")
    print("=" * 50)
    
    # 测试高级API
    test_texts = [
        "这是**重要**的`代码示例`",
        "数学公式 $\\alpha + \\beta = \\gamma$ 很有用",
        "~~过时的~~信息和*新的*内容",
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n测试文本 {i}: '{text}'")
        
        # 测试样式提取
        styles = extract_text_styles(text)
        print(f"  提取的样式: {styles}")
        
        # 测试纯文本提取
        clean_text = get_clean_text(text)
        print(f"  纯文本: '{clean_text}'")


def test_text_style_processor():
    """测试TextStyleProcessor类"""
    print("\n" + "=" * 50)
    print("测试 TextStyleProcessor 类")
    print("=" * 50)
    
    # 创建处理器实例
    processor = TextStyleProcessor(
        default_font_size=36,
        default_animation_duration=1.5
    )
    
    # 测试单个文本处理
    test_text = "这是**加粗**和`代码`的混合文本"
    print(f"\n处理文本: '{test_text}'")
    
    try:
        result = processor.process_text(
            markdown_text=test_text,
            mobject_type="test_annotation",
            animation_config={"duration": 2.0}
        )
        
        print(f"  处理结果:")
        print(f"    纯文本: '{result['clean_text']}'")
        print(f"    样式信息: {result['styles']}")
        print(f"    有样式: {result['has_styles']}")
        print(f"    动画配置: {result['animation_config']}")
        print(f"    对象类型: {result['mobject_type']}")
        
    except Exception as e:
        print(f"  处理出错: {e}")
    
    # 测试批量处理
    batch_texts = [
        "第一个**重要**文本",
        "第二个包含`代码`的文本", 
        "第三个有*斜体*的文本"
    ]
    
    print(f"\n批量处理 {len(batch_texts)} 个文本:")
    try:
        results = processor.batch_process(batch_texts)
        
        for i, result in enumerate(results):
            if 'error' in result:
                print(f"  文本 {i+1}: 处理失败 - {result['error']}")
            else:
                print(f"  文本 {i+1}: '{result['clean_text']}' -> {list(result['styles'].keys())}")
                
    except Exception as e:
        print(f"  批量处理出错: {e}")


def test_edge_cases():
    """测试边界情况和错误处理"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    edge_cases = [
        None,  # None值
        "",    # 空字符串
        "   ",  # 只有空格
        "**",   # 只有格式标记
        "****", # 重复格式标记
        "**嵌套的*斜体*在粗体**中",  # 嵌套格式
        "`代码中的**粗体**不应该解析`",  # 代码中的格式
        "$数学公式中的**粗体**也不应该解析$",  # 数学公式中的格式
        "多行\n文本\n测试**粗体**",  # 多行文本
        "Unicode测试：**中文粗体**、*日本語*、`코드`",  # Unicode字符
    ]
    
    for i, text in enumerate(edge_cases, 1):
        print(f"\n边界测试 {i}: {repr(text)}")
        try:
            if text is None:
                # 测试None值处理
                clean_text, styles = parse_text_styles("")
                print(f"  None处理: 纯文本='{clean_text}', 样式={styles}")
            else:
                clean_text, styles = parse_text_styles(text)
                print(f"  纯文本: '{clean_text}'")
                print(f"  样式信息: {styles}")
                
                # 验证语法
                issues = validate_markdown_syntax(text)
                if issues:
                    print(f"  语法问题: {issues}")
                    
        except Exception as e:
            print(f"  处理异常: {e}")


def test_performance():
    """测试性能（简单测试）"""
    print("\n" + "=" * 50)
    print("测试性能")
    print("=" * 50)
    
    import time
    
    # 生成大量测试数据
    large_text = "这是**加粗**和*斜体*以及`代码`的文本。" * 100
    
    print(f"测试大文本处理（长度: {len(large_text)}）")
    
    start_time = time.time()
    for _ in range(10):
        clean_text, styles = parse_text_styles(large_text)
    end_time = time.time()
    
    print(f"  10次解析耗时: {end_time - start_time:.4f} 秒")
    print(f"  平均每次: {(end_time - start_time) / 10:.4f} 秒")
    print(f"  解析结果: {len(clean_text)} 字符, {sum(len(v) for v in styles.values())} 个样式")


def main():
    """运行所有测试"""
    print("Text Style System 功能测试")
    print("=" * 60)
    
    try:
        test_parser_module()
        test_interface_module()
        test_text_style_processor()
        test_edge_cases()
        test_performance()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
