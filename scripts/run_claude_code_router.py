#!/usr/bin/env python3
"""
Claude Code SDK脚本 - 优化版本
用于通过Anthropic SDK调用Claude生成manim代码并确保视频生成

主要优化:
1. 强制执行manim渲染：任务描述明确要求必须执行manim命令
2. 多重验证机制：Claude执行完成后验证视频文件是否生成
3. 自动备用执行：如果Claude没有执行manim，脚本会自动执行
4. 详细错误反馈：提供具体的失败原因和故障排除建议
5. 最终验证步骤：确保最终结果包含有效的视频文件

使用方法:
python scripts/run_claude_code_router.py <scene_file> <output_file> [--video_output_path <path>]

参数:
- scene_file: 场景描述文件路径
- output_file: 输出的Python文件名（不含扩展名）
- --video_output_path, -v: 可选，视频输出基础目录（默认使用media目录，然后移动到generated_files）

示例:
python scripts/run_claude_code_router.py scene.txt my_animation
python scripts/run_claude_code_router.py scene.txt my_animation --video_output_path /custom/output/path

执行流程:
1. 检查依赖和服务
2. 构建强制性任务描述
3. 通过Claude SDK生成代码
4. 验证代码文件是否生成
5. 验证视频文件是否生成
6. 如果视频未生成，自动执行manim命令
7. 最终验证并移动视频文件到指定位置
"""

import argparse
import json
import os
import subprocess
import sys
from pathlib import Path
from typing import Optional

import anyio
from loguru import logger

sys.path.append(os.getcwd())

from utils.common import Config

try:
    from claude_code_sdk import ClaudeCodeOptions, Message, query
    from claude_code_sdk.types import (
        AssistantMessage,
        ResultMessage,
        SystemMessage,
        TextBlock,
        ToolResultBlock,
        ToolUseBlock,
        UserMessage,
    )
except ImportError:
    logger.error("❌ claude-code-sdk未安装，请运行: pip install claude-code-sdk")
    sys.exit(1)


def check_and_install(bin: str = "ccr") -> bool:
    """检查ccr命令是否存在，不存在则安装"""
    try:
        # 检查ccr命令是否存在
        result = subprocess.run([bin, "-v"], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ {bin} 已安装")
            return True
    except FileNotFoundError:
        pass

    logger.info(f"📦 正在安装{bin}...")
    try:
        # 安装claude-code-router
        if bin == "ccr":
            target = "@musistudio/claude-code-router"
        elif bin == "claude":
            target = "@anthropic-ai/claude-code"
        else:
            raise ValueError(f"不支持的二进制文件名: {bin}")
        result = subprocess.run(["npm", "install", "-g", target], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ {bin} 安装成功")
            return True
        else:
            logger.error(f"❌ 安装失败: {result.stderr}")
            return False
    except FileNotFoundError:
        logger.error("❌ npm未找到，请先安装Node.js和npm")
        return False


def check_dependencies() -> bool:
    """检查必要的依赖是否已安装"""
    try:
        # 检查claude-code-sdk是否可用
        from claude_code_sdk import ClaudeCodeOptions, Message, query  # noqa: F401

        logger.info("✅ claude-code-sdk 已安装")
        return True
    except ImportError:
        logger.error("❌ claude-code-sdk未安装，请运行: pip install claude-code-sdk")
        return False


def setup_config() -> bool:
    """复制配置文件到指定位置"""
    source_config = Path("config/claude_code_router_config.json")
    target_dir = Path.home() / ".claude-code-router"
    target_config = target_dir / "config.json"

    try:
        # 创建目标目录
        target_dir.mkdir(parents=True, exist_ok=True)

        # 复制配置文件
        import shutil

        shutil.copy2(source_config, target_config)
        logger.info(f"✅ 配置文件已复制到 {target_config}")
        return True
    except Exception as e:
        logger.error(f"❌ 配置文件复制失败: {e}")
        return False


def read_file_content(file_path: str, description: str) -> str:
    """通用文件内容读取函数"""
    try:
        with open(file_path, encoding="utf-8") as f:
            content = f.read().strip()
        logger.info(f"✅ 已读取{description}: {file_path}")
        return content
    except Exception as e:
        logger.error(f"❌ 读取{description}失败: {e}")
        return ""


def read_scene_file(scene_file: str) -> str:
    """读取场景描述文件内容"""
    return read_file_content(scene_file, "场景文件")


def read_common_errors() -> str:
    """读取常见错误文件内容"""
    error_file = Path("config/manim_common_errors.txt")
    return read_file_content(str(error_file), "常见错误文件")


def read_code_template() -> str:
    """读取代码生成模板文件内容"""
    template_file = Path("docs/code_generate_template_0731.txt")
    return read_file_content(str(template_file), "代码生成模板文件")


def build_task_description(scene_content: str, output_file: str, common_errors: str, code_template: str, video_output_path: Optional[str] = None) -> str:
    """构建简洁的任务描述，避免shell特殊字符"""
    # 清理常见错误文本，移除可能导致问题的字符
    cleaned_errors = common_errors.replace("(", "").replace(")", "").replace("[", "").replace("]", "").replace("#", "")

    # 构建渲染命令，统一使用manim默认路径
    render_cmd = "manim -pql"
    if video_output_path:
        output_instruction = f"\n\n注意: 生成的视频将自动移动到指定目录: {video_output_path}"
    else:
        output_instruction = "\n\n注意: 生成的视频将自动移动到generated_files目录"

    base_description = f"""任务: 生成Manim代码并渲染视频

目标文件: {output_file}.py
渲染命令: {render_cmd}{output_instruction}

场景描述:
{scene_content}

代码生成模板参考，必须严格遵循代码规范:
{code_template}

关键执行要求 - 必须按顺序完成以下所有步骤:
1. 一步到位生成完整可运行的Manim代码，避免多轮交互
2. 将代码写入到文件 {output_file}.py
3. 立即执行manim渲染命令: {render_cmd} {output_file}.py
4. 验证视频文件已成功生成

重要提醒:
- 步骤3是强制性的，必须执行manim命令生成视频
- 跳过中间步骤: 无需todo、规划、总结，完成后只返回success
- 如果manim命令执行失败，必须报告错误原因
- 参考代码生成模板的指导原则和最佳实践，必须继承ProfessionalScienceTemplate类: `from prompts.professional_science_template import ProfessionalScienceTemplate`

常见错误避免:
{cleaned_errors}

执行流程: 写入代码文件 → 执行manim渲染 → 验证视频生成 → 返回结果"""

    return base_description


def execute_manim_manually(code_file: str, quality: str = "l", video_output_base: Optional[str] = None) -> Optional[str]:
    """
    手动执行manim命令生成视频
    
    Args:
        code_file: Python代码文件路径
        quality: 视频质量参数 (l, m, h, q, k)
        video_output_base: 自定义视频输出基础目录
        
    Returns:
        成功生成的视频文件路径，失败时返回None
    """
    try:
        code_path = Path(code_file)
        if not code_path.exists():
            logger.error(f"❌ 代码文件不存在: {code_file}")
            return None
        
        # 构建manim命令
        quality_flag = f"-pq{quality}"
        cmd = ["manim", quality_flag, str(code_path)]
        
        logger.info(f"🎬 执行manim命令: {' '.join(cmd)}")
        
        # 执行manim命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            cwd=code_path.parent
        )
        
        if result.returncode == 0:
            logger.info("✅ manim命令执行成功")
            logger.info(f"📝 manim输出: {result.stdout}")
            
            # 查找生成的视频文件
            video_file = find_generated_video_file(code_file, quality, video_output_base)
            if video_file:
                return video_file
            else:
                logger.error("❌ manim执行成功但未找到生成的视频文件")
                return None
        else:
            logger.error(f"❌ manim命令执行失败 (退出码: {result.returncode})")
            logger.error(f"📝 错误输出: {result.stderr}")
            logger.error(f"📝 标准输出: {result.stdout}")
            return None
            
    except subprocess.TimeoutExpired:
        logger.error("⏰ manim命令执行超时 (5分钟)")
        return None
    except Exception as e:
        logger.error(f"❌ 执行manim命令时出错: {e}")
        return None


def find_generated_video_file(code_file: str, quality: str = "l", video_output_base: Optional[str] = None) -> Optional[str]:
    """
    根据代码文件路径查找生成的视频文件，并将其移动到指定目录
    参考unified_feynman_workflow.py中的逻辑
    
    Args:
        code_file: 代码文件路径
        quality: 视频质量
        video_output_base: 自定义视频输出基础目录，如果为None则移动到generated_files目录
    """
    try:
        code_path = Path(code_file)
        if not code_path.exists():
            return None

        # 根据质量参数确定分辨率目录
        resolution = {
            "l": "480p15",
            "m": "720p30",
            "h": "1080p60",
            "q": "1440p60",
            "k": "2160p60",
        }.get(quality, "480p15")

        # 在manim的默认输出路径查找视频文件
        manim_output_path = Path("media") / "videos" / code_path.stem / resolution
        
        if manim_output_path.exists():
            video_files = list(manim_output_path.glob("*.mp4"))
            if video_files:
                # 按修改时间排序，取最新的文件
                original_video = max(video_files, key=lambda f: f.stat().st_mtime)
                logger.info(f"🎥 找到生成的视频文件: {original_video}")
                
                # 确定目标目录
                if video_output_base:
                    # 使用指定的video_output_base目录
                    target_dir = Path(video_output_base)
                    target_dir.mkdir(parents=True, exist_ok=True)
                    logger.info(f"📁 目标目录: {target_dir}")
                else:
                    # 使用默认的generated_files目录
                    target_dir = Path("generated_files")
                    target_dir.mkdir(exist_ok=True)
                    logger.info(f"📁 目标目录: {target_dir}")
                
                # 构建目标路径：使用原文件名
                target_video_path = target_dir / original_video.name
                
                # 如果目标文件已存在，生成唯一文件名
                counter = 1
                while target_video_path.exists():
                    name_parts = original_video.stem, counter, original_video.suffix
                    target_video_path = target_dir / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                    counter += 1
                
                # 使用rename移动文件到目标目录
                original_video.rename(target_video_path)
                logger.info(f"📦 视频文件已移动到: {target_video_path}")
                
                return str(target_video_path)

        return None
    except Exception as e:
        logger.error(f"⚠️ 查找或移动视频文件时出错: {e}")
        return None


def start_ccr_service() -> bool:
    """启动CCR服务（后台运行）"""
    try:
        logger.info("🔧 启动CCR服务...")
        # 使用Popen启动后台服务，不等待完成
        process = subprocess.Popen(["ccr", "start"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        # 等待一小段时间确保服务启动
        import time

        time.sleep(3)

        # 检查进程是否还在运行（如果立即退出可能是错误）
        if process.poll() is None:
            logger.info("✅ CCR服务已在后台启动")
            return True
        else:
            # 进程已退出，检查输出
            stdout, stderr = process.communicate()
            if "already running" in stderr.lower() or "already running" in stdout.lower():
                logger.info("✅ CCR服务已在运行")
                return True
            elif process.returncode == 0:
                logger.info("✅ CCR服务启动成功")
                return True
            else:
                logger.warning(f"⚠️ CCR服务启动警告: {stderr}")
                # 即使有警告也继续执行，服务可能已经在运行
                return True

    except Exception as e:
        logger.error(f"❌ 启动CCR服务失败: {e}")
        return False


def stop_ccr_service() -> None:
    """停止CCR服务"""
    try:
        logger.info("🛑 停止CCR服务...")
        result = subprocess.run(["ccr", "stop"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info("✅ CCR服务已停止")
        else:
            logger.warning(f"⚠️ 停止CCR服务时出现警告: {result.stderr}")
    except subprocess.TimeoutExpired:
        logger.error("⏰ 停止CCR服务超时")
    except Exception as e:
        logger.error(f"⚠️ 停止CCR服务时出错: {e}")


async def run_claude_sdk(
    task_description: str, output_file: str, quality: str = "l", use_cheap_model: bool = False, video_output_base: Optional[str] = None
) -> dict[str, Optional[str]]:
    """
    使用Claude Code SDK运行任务

    Args:
        task_description: 任务描述
        output_file: 输出文件名（不含扩展名）
        quality: 渲染质量参数

    Returns:
        包含生成结果的字典
    """
    try:
        print("🚀 正在运行Claude SDK...")

        # 设置环境变量
        if use_cheap_model:
            os.environ["ANTHROPIC_BASE_URL"] = "https://jeniya.cn"
        else:
            os.environ["ANTHROPIC_BASE_URL"] = "http://127.0.0.1:3456"
        os.environ["ANTHROPIC_API_KEY"] = "sk-8OfEbuUlsP78FeqVwPVgsgXxjpRVDQlf3fpmP6wNRm3qQwDs"

        # 配置SDK选项
        options = ClaudeCodeOptions(
            cwd=Path.cwd(),  # 当前工作目录
            permission_mode="bypassPermissions",  # 跳过权限检查，对应--dangerously-skip-permissions
        )

        # 收集所有消息
        messages: list[Message] = []

        # 使用SDK查询
        async for message in query(prompt=task_description, options=options):
            messages.append(message)
            # 实时显示进度
            if isinstance(message, UserMessage):
                result = str(message.content)[:100] + "..." if len(str(message.content)) > 100 else str(message.content)
                logger.info(f"👤 用户消息已发送: {result}")
            elif isinstance(message, AssistantMessage):
                # 分析助手消息内容
                text_blocks = [block for block in message.content if isinstance(block, TextBlock)]
                tool_use_blocks = [block for block in message.content if isinstance(block, ToolUseBlock)]
                tool_result_blocks = [block for block in message.content if isinstance(block, ToolResultBlock)]

                if tool_use_blocks:
                    tool_names = [block.name for block in tool_use_blocks]
                    logger.info(f"🔧 Claude正在使用工具: {', '.join(tool_names)}")
                elif text_blocks:
                    # 显示文本内容的简短摘要
                    text_content = (
                        text_blocks[0].text[:100] + "..." if len(text_blocks[0].text) > 100 else text_blocks[0].text
                    )
                    logger.info(f"🤖 Claude回复: {text_content}")
                else:
                    logger.info("🤖 Claude正在处理...")
                if tool_result_blocks:
                    result = str(tool_result_blocks[0].content)
                    result = result[:100] + "..." if len(result) > 100 else result
                    logger.info(f"🔧 工具返回结果: {result}")

            elif isinstance(message, SystemMessage):
                if message.subtype == "init":
                    logger.info(f"🚀 系统初始化完成 - 会话ID: {message.data.get('session_id', 'unknown')}")
                    if "tools" in message.data:
                        logger.info(f"🛠️  可用工具: {len(message.data['tools'])} 个")
                    if "model" in message.data:
                        logger.info(f"🧠 使用模型: {message.data['model']}")
                else:
                    logger.info(f"ℹ️  系统消息: {message.subtype}")

            elif isinstance(message, ResultMessage):
                if message.subtype == "success":
                    logger.info("✅ Claude SDK执行成功")
                    logger.info(f"📊 执行统计: {message.num_turns}轮对话, 耗时{message.duration_ms}ms")
                    if message.total_cost_usd:
                        logger.info(f"💰 成本: ${message.total_cost_usd:.4f}")
                elif message.subtype == "error_max_turns":
                    logger.warning(f"⚠️ 达到最大轮数限制({message.num_turns}轮)")
                elif message.subtype == "error_during_execution":
                    logger.error("❌ 执行过程中出现错误")
                else:
                    logger.warning(f"⚠️ Claude SDK执行完成，状态: {message.subtype}")

                # 显示通用统计信息
                if message.duration_ms > 0:
                    logger.info(
                        f"⏱️  总耗时: {message.duration_ms/1000:.1f}秒 (API: {message.duration_api_ms/1000:.1f}秒)"
                    )
            else:
                logger.info(f"📨 收到消息: {type(message).__name__}")

        # 检查执行结果
        if messages:
            # 获取最后一条结果消息
            result_message = None
            for msg in reversed(messages):
                if isinstance(msg, ResultMessage):
                    result_message = msg
                    break

            if result_message and result_message.subtype == "success":
                # 直接使用传入的output_file参数构建代码文件路径
                code_file = f"{output_file}.py"

                # 检查生成的代码文件是否存在
                if os.path.exists(code_file):
                    code_file = os.path.abspath(code_file)
                    logger.info(f"📄 找到生成的代码文件: {code_file}")

                    # 查找对应的视频文件
                    video_file = find_generated_video_file(code_file, quality, video_output_base)
                    if video_file:
                        logger.info(f"🎥 找到生成的视频文件: {video_file}")
                        return {"final_code_path": code_file, "final_video_path": video_file, "success": True}
                    else:
                        logger.warning("⚠️ 未找到对应的视频文件，尝试手动执行manim命令")
                        # 如果没有找到视频文件，手动执行manim命令
                        manual_video_file = execute_manim_manually(code_file, quality, video_output_base)
                        if manual_video_file:
                            logger.info(f"✅ 手动执行manim成功，生成视频: {manual_video_file}")
                            return {"final_code_path": code_file, "final_video_path": manual_video_file, "success": True}
                        else:
                            logger.error("❌ 手动执行manim失败，无法生成视频")
                            return {"final_code_path": code_file, "final_video_path": None, "success": False}
                else:
                    logger.error(f"❌ 预期的代码文件不存在: {code_file}")
                    return {"final_code_path": None, "final_video_path": None, "success": False}
            elif result_message:
                # 有结果消息但不是成功状态
                logger.error(f"❌ Claude SDK执行失败: {result_message.subtype}")
                if result_message.subtype == "error_max_turns":
                    logger.error(f"达到最大轮数限制: {result_message.num_turns}轮")
                elif result_message.subtype == "error_during_execution":
                    logger.error("执行过程中出现错误")
                return {"final_code_path": None, "final_video_path": None, "success": False}
            else:
                logger.error("❌ 未收到结果消息，执行可能未完成")
                return {"final_code_path": None, "final_video_path": None, "success": False}
        else:
            logger.error("❌ 未收到任何消息")
            return {"final_code_path": None, "final_video_path": None, "success": False}

    except Exception as e:
        logger.error(f"❌ 运行Claude SDK时出错: {e}")
        return {"final_code_path": None, "final_video_path": None, "success": False}


def run_ccr(
    task_description: str, output_file: str, quality: str = "l", use_cheap_model: bool = False, video_output_base: Optional[str] = None
) -> dict[str, Optional[str]]:
    """
    运行Claude SDK的同步包装函数

    Args:
        task_description: 任务描述
        output_file: 输出文件名（不含扩展名）
        quality: 渲染质量参数

    Returns:
        包含生成结果的字典
    """
    return anyio.run(run_claude_sdk, task_description, output_file, quality, use_cheap_model, video_output_base)


def process_scene_with_ccr(scene_file: str, output_file: str, quality: str = "l", video_output_path: Optional[str] = None) -> dict[str, Optional[str]]:
    """
    使用Claude Code SDK处理场景文件的包装函数

    Args:
        scene_file: 场景描述文件路径
        output_file: 输出的Python文件名（不含扩展名）
        quality: 渲染质量 (l, m, h, q, k)
        video_output_path: 视频输出路径，默认使用manim的media目录，然后移动到generated_files

    Returns:
        包含生成结果的字典，格式与process_scene_file_enhanced相同
    """
    config = Config().config.get("workflow", {}).get("code_agent", {})
    use_cheap_model = config.get("use_cheap_model", False)
    # 1. 检查并安装ccr和Claude
    if not use_cheap_model:
        if not check_and_install("ccr"):
            return {"final_code_path": None, "final_video_path": None, "success": False}
    if not check_and_install("claude"):
        return {"final_code_path": None, "final_video_path": None, "success": False}

    # 2. 检查SDK依赖
    if not check_dependencies():
        return {"final_code_path": None, "final_video_path": None, "success": False}

    # # 3. 设置配置文件
    if not use_cheap_model:
        if not setup_config():
            return {"final_code_path": None, "final_video_path": None, "success": False}

    # # 4. 启动CCR服务
    if not use_cheap_model:
        if not start_ccr_service():
            return {"final_code_path": None, "final_video_path": None, "success": False}

    # 5. 读取场景文件
    scene_content = read_scene_file(scene_file)
    if not scene_content:
        return {"final_code_path": None, "final_video_path": None, "success": False}

    # 6. 读取常见错误
    common_errors = read_common_errors()

    # 7. 读取代码生成模板
    code_template = read_code_template()

    # 8. 构建任务描述
    target_file = Path(output_file + ".py")
    target_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 构建任务描述，manim将使用默认路径执行，完成后移动到指定目录
    task_description = build_task_description(scene_content, output_file, common_errors, code_template, video_output_path)

    # 9. 运行Claude SDK
    try:
        result = run_ccr(task_description, output_file, quality, use_cheap_model, video_output_path)
        
        # 10. 最终验证：确保视频文件已生成
        if result["success"] and result.get("final_code_path") and not result.get("final_video_path"):
            logger.warning("⚠️ Claude SDK报告成功但未找到视频文件，进行最后尝试")
            manual_video_file = execute_manim_manually(result["final_code_path"], quality, video_output_path)
            if manual_video_file:
                logger.info("✅ 最终验证：手动执行manim成功")
                result["final_video_path"] = manual_video_file
                result["success"] = True
            else:
                logger.error("❌ 最终验证：手动执行manim也失败")
                result["success"] = False
        
        return result
    except Exception as e:
        logger.error(f"❌ 运行Claude SDK时出错: {e}")
        return {"final_code_path": None, "final_video_path": None, "success": False}
    finally:
        # 11. 停止CCR服务
        if not use_cheap_model:
            stop_ccr_service()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="使用Claude Code SDK生成manim代码")
    parser.add_argument("scene_file", help="场景描述文件路径")
    parser.add_argument("output_file", help="输出的Python文件名（不含扩展名）")
    parser.add_argument("--video_output_path", "-v", help="视频输出基础目录（默认使用media，然后移动到generated_files）", default=None)

    args = parser.parse_args()

    print("🎬 Claude Code SDK Manim代码生成器")
    print("=" * 50)

    # 使用包装函数处理
    result = process_scene_with_ccr(args.scene_file, args.output_file, quality="l", video_output_path=args.video_output_path)

    # 输出结果
    print("\n" + "=" * 50)
    print("📊 执行结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    if result["success"]:
        print("🎉 任务完成！")
        if result.get("final_code_path"):
            print(f"📄 代码文件: {result['final_code_path']}")
        if result.get("final_video_path"):
            print(f"🎥 视频文件: {result['final_video_path']}")
        else:
            print("⚠️ 警告: 代码生成成功但视频文件未找到")
    else:
        print("💥 任务失败！")
        # 提供详细的失败原因
        if not result.get("final_code_path"):
            print("❌ 错误: 代码文件生成失败")
            print("💡 建议: 检查场景描述是否清晰，或查看claude-code-sdk的日志")
        elif not result.get("final_video_path"):
            print("❌ 错误: 视频生成失败")
            print("💡 建议: 检查manim是否正确安装，代码是否有语法错误")
            if result.get("final_code_path"):
                print(f"📄 可手动运行: manim -pql {result['final_code_path']}")
        
        print("\n🔍 故障排除步骤:")
        print("1. 检查场景描述文件是否存在且内容清晰")
        print("2. 确认manim已正确安装: pip install manim")
        print("3. 检查claude-code-sdk服务是否正常运行")
        print("4. 查看生成的代码文件是否有语法错误")
        
        sys.exit(1)


if __name__ == "__main__":
    main()
