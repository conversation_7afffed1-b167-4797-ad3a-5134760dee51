#!/usr/bin/env python3
"""
测试animate_image.py与text_style_system的集成

这个测试文件验证animate_image.py中的annotation功能是否正确集成了markdown样式支持。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dsl.v2.animation_functions.animate_image import _create_enhanced_text, _apply_text_style_animations
from dsl.v2.utils.text_style_system import has_styled_text_content


def test_enhanced_text_creation():
    """测试增强文本创建功能"""
    print("=" * 50)
    print("测试增强文本创建功能")
    print("=" * 50)
    
    test_cases = [
        # (文本内容, 是否应该有样式, 描述)
        ("普通文本没有任何格式", False, "普通文本"),
        ("这是**加粗**的文本", True, "包含加粗"),
        ("这是*斜体*的文本", True, "包含斜体"),
        ("这是`代码`的文本", True, "包含代码"),
        ("这是~~删除线~~的文本", True, "包含删除线"),
        ("数学公式 $E=mc^2$ 很有用", True, "包含数学公式"),
        ("链接 [百度](https://baidu.com) 测试", True, "包含链接"),
        ("复杂文本：**粗体**、*斜体*、`代码`", True, "多种样式混合"),
        ("emoji测试 😀 没有markdown", False, "包含emoji但无markdown"),
    ]
    
    for i, (text, should_have_styles, description) in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {description}")
        print(f"  输入文本: '{text}'")
        
        try:
            # 测试启用markdown的情况
            text_obj_with_markdown = _create_enhanced_text(
                text_content=text,
                font_size=36,
                enable_markdown=True
            )
            
            has_styles = has_styled_text_content(text_obj_with_markdown)
            print(f"  启用markdown - 有样式: {has_styles}")
            print(f"  显示文本: '{text_obj_with_markdown.text}'")
            
            if has_styles != should_have_styles:
                print(f"  ⚠️  警告: 期望有样式={should_have_styles}, 实际={has_styles}")
            else:
                print(f"  ✅ 样式检测正确")
            
            # 测试禁用markdown的情况
            text_obj_without_markdown = _create_enhanced_text(
                text_content=text,
                font_size=36,
                enable_markdown=False
            )
            
            has_styles_disabled = has_styled_text_content(text_obj_without_markdown)
            print(f"  禁用markdown - 有样式: {has_styles_disabled}")
            
            if has_styles_disabled:
                print(f"  ⚠️  警告: 禁用markdown时不应该有样式")
            else:
                print(f"  ✅ 禁用markdown正确")
                
        except Exception as e:
            print(f"  ❌ 错误: {e}")


def test_annotation_scenarios():
    """测试annotation的实际使用场景"""
    print("\n" + "=" * 50)
    print("测试Annotation使用场景")
    print("=" * 50)
    
    # 模拟不同类型的annotation
    annotation_scenarios = [
        {
            "type": "技术说明",
            "content": "这是**重要**的`API接口`说明",
            "expected_styles": ["bold", "code"]
        },
        {
            "type": "数学公式",
            "content": "计算公式：$\\alpha + \\beta = \\gamma$",
            "expected_styles": ["math"]
        },
        {
            "type": "多行说明",
            "content": [
                "第一行包含**加粗**文本",
                "第二行包含`代码示例`",
                "第三行包含*斜体*强调"
            ],
            "expected_styles": ["bold", "code", "italic"]
        },
        {
            "type": "混合格式",
            "content": "~~过时的~~信息，请使用**新的**`API`接口",
            "expected_styles": ["strikethrough", "bold", "code"]
        }
    ]
    
    for i, scenario in enumerate(annotation_scenarios, 1):
        print(f"\n场景 {i}: {scenario['type']}")
        
        if isinstance(scenario['content'], list):
            # 处理列表格式的annotation
            print(f"  多行内容:")
            for j, line in enumerate(scenario['content']):
                print(f"    {j+1}. '{line}'")
                
                try:
                    text_obj = _create_enhanced_text(line, font_size=32)
                    has_styles = has_styled_text_content(text_obj)
                    print(f"       有样式: {has_styles}")
                    
                    if hasattr(text_obj, 'text_styles'):
                        styles = list(text_obj.text_styles.keys())
                        print(f"       样式类型: {styles}")
                    
                except Exception as e:
                    print(f"       错误: {e}")
        else:
            # 处理字符串格式的annotation
            print(f"  内容: '{scenario['content']}'")
            
            try:
                text_obj = _create_enhanced_text(scenario['content'], font_size=32)
                has_styles = has_styled_text_content(text_obj)
                print(f"  有样式: {has_styles}")
                
                if hasattr(text_obj, 'text_styles'):
                    styles = list(text_obj.text_styles.keys())
                    print(f"  样式类型: {styles}")
                    
                    # 检查是否包含期望的样式
                    expected = set(scenario['expected_styles'])
                    actual = set(styles)
                    
                    if expected.issubset(actual):
                        print(f"  ✅ 包含期望的样式: {expected}")
                    else:
                        missing = expected - actual
                        print(f"  ⚠️  缺少样式: {missing}")
                        
                    extra = actual - expected
                    if extra:
                        print(f"  ℹ️  额外样式: {extra}")
                
            except Exception as e:
                print(f"  ❌ 错误: {e}")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n" + "=" * 50)
    print("测试向后兼容性")
    print("=" * 50)
    
    # 测试原有的annotation格式是否仍然工作
    legacy_annotations = [
        "简单的文本注释",
        "包含emoji的注释 😀 🎉",
        "多行\n文本\n注释",
        ["列表格式", "的注释", "内容"],
        "",  # 空注释
    ]
    
    for i, annotation in enumerate(legacy_annotations, 1):
        print(f"\n兼容性测试 {i}: {type(annotation).__name__}")
        
        if isinstance(annotation, list):
            print(f"  列表内容: {annotation}")
            for j, item in enumerate(annotation):
                try:
                    text_obj = _create_enhanced_text(item, font_size=32)
                    print(f"    项目 {j+1}: '{text_obj.text}' - 成功")
                except Exception as e:
                    print(f"    项目 {j+1}: 失败 - {e}")
        else:
            print(f"  内容: {repr(annotation)}")
            try:
                text_obj = _create_enhanced_text(annotation, font_size=32)
                print(f"  结果: '{text_obj.text}' - 成功")
            except Exception as e:
                print(f"  结果: 失败 - {e}")


def test_performance_comparison():
    """测试性能对比"""
    print("\n" + "=" * 50)
    print("测试性能对比")
    print("=" * 50)
    
    import time
    
    # 测试文本
    test_text = "这是**加粗**和*斜体*以及`代码`的文本"
    iterations = 50
    
    print(f"测试文本: '{test_text}'")
    print(f"迭代次数: {iterations}")
    
    # 测试启用markdown的性能
    start_time = time.time()
    for _ in range(iterations):
        text_obj = _create_enhanced_text(test_text, enable_markdown=True)
    markdown_time = time.time() - start_time
    
    # 测试禁用markdown的性能
    start_time = time.time()
    for _ in range(iterations):
        text_obj = _create_enhanced_text(test_text, enable_markdown=False)
    no_markdown_time = time.time() - start_time
    
    print(f"\n性能结果:")
    print(f"  启用markdown: {markdown_time:.4f} 秒 (平均 {markdown_time/iterations:.4f} 秒/次)")
    print(f"  禁用markdown: {no_markdown_time:.4f} 秒 (平均 {no_markdown_time/iterations:.4f} 秒/次)")
    print(f"  性能差异: {(markdown_time/no_markdown_time - 1)*100:.1f}%")


def main():
    """运行所有测试"""
    print("Animate Image Integration 测试")
    print("=" * 60)
    
    try:
        test_enhanced_text_creation()
        test_annotation_scenarios()
        test_backward_compatibility()
        test_performance_comparison()
        
        print("\n" + "=" * 60)
        print("所有集成测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
