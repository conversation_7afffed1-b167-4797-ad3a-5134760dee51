🚀 启动Feynman视频生成服务...
📡 服务将在 http://localhost:8000 启动
📖 API文档地址: http://localhost:8000/docs
🔧 健康检查: http://localhost:8000/health

按 Ctrl+C 停止服务

   Building ibm-cos-sdk==2.14.3
Downloading virtualenv (5.7MiB)
Downloading mypy (9.6MiB)
Downloading litellm (8.5MiB)
Downloading sympy (6.0MiB)
Downloading pandas (10.2MiB)
Downloading duckdb (14.8MiB)
Downloading ruff (11.2MiB)
Downloading docling-parse (13.9MiB)
Downloading milvus-lite (23.3MiB)
Downloading torch (70.2MiB)
Downloading cryptography (6.7MiB)
Downloading pymupdf (21.4MiB)
Downloading onnxruntime (32.8MiB)
Downloading selenium (9.2MiB)
Downloading grpcio (10.5MiB)
Downloading transformers (10.7MiB)
Downloading gradio (56.9MiB)
Downloading mlx-metal (31.6MiB)
Downloading playwright (36.9MiB)
Downloading pdfminer-six (5.4MiB)
Downloading pydantic-core (1.8MiB)
Downloading lxml (7.8MiB)
Downloading tokenizers (2.6MiB)
Downloading pyarrow (29.7MiB)
Downloading matplotlib (7.7MiB)
Downloading opencv-python-headless (36.1MiB)
Downloading opencv-python (36.1MiB)
Downloading cython (2.7MiB)
Downloading scikit-learn (8.2MiB)
Downloading scipy (19.9MiB)
      Built ibm-cos-sdk==2.14.3
Downloading botocore (13.4MiB)
   Building ibm-cos-sdk-s3transfer==2.14.3
      Built ibm-cos-sdk-s3transfer==2.14.3
   Building pycairo==1.28.0
   Building ibm-cos-sdk-core==2.14.3
      Built ibm-cos-sdk-core==2.14.3
 Downloaded pydantic-core
 Downloaded tokenizers
 Downloaded cython
      Built pycairo==1.28.0
 Downloaded pdfminer-six
 Downloaded virtualenv
 Downloaded sympy
 Downloaded cryptography
./start_video_service.sh: line 30: 73282 Terminated: 15          uv run python video_generation_service.py
