<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>SVG 路径动画</title>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f0f0f0;
    }

    svg {
      border: 1px solid #ccc;
      box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }

    /* 动画定义 */
    @keyframes draw-in {
      /* 动画结束时，偏移量为 0 */
      to {
        stroke-dashoffset: 0;
      }
    }

    /* 将动画应用到我们的路径上 */
    #my-path {
      /* 
       * stroke-dasharray 和 stroke-dashoffset 将由 JS 动态设置
       * 在这里我们先定义动画本身
       */
      animation: draw-in 2s ease-in-out forwards;
    }
  </style>
</head>
<body>

  <!-- 这里我们直接嵌入由 Python 生成的 SVG 内容 -->
  <!-- 注意：你需要先运行 Python 脚本生成 drawing.svg，然后把内容复制过来 -->
  <!-- 或者你也可以修改 Python 脚本，让它直接生成带 <style> 和 <script> 的完整 HTML 文件 -->
  <svg width="200px" height="200px" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path d="M 10 10 L 190 190" id="my-path" stroke="crimson" stroke-width="3" fill="none"/>
  </svg>

  <script>
    // 等待文档加载完成
    document.addEventListener('DOMContentLoaded', () => {
      // 1. 通过 ID 获取路径元素
      const path = document.querySelector('#my-path');
      
      if (path) {
        // 2. 获取路径的总长度
        const pathLength = path.getTotalLength();
        console.log('路径总长度:', pathLength); // 在开发者工具中查看

        // 3. 设置 stroke-dasharray 和 stroke-dashoffset
        //    这将隐藏路径，为动画做准备
        path.style.strokeDasharray = pathLength;
        path.style.strokeDashoffset = pathLength;

        // 4. CSS @keyframes 动画会自动开始播放
        //    将 stroke-dashoffset 从 pathLength 动画到 0
      }
    });
  </script>

</body>
</html>
