[Unit]
Description=Feynman Video Generation Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=nazgul
WorkingDirectory=/Users/<USER>/Documents/codes/agentic-feynman
ExecStart=/Users/<USER>/Documents/codes/agentic-feynman/monitor_video_service.sh
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=feynman-video

# 环境变量
Environment=TAVILY_API_KEY=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
Environment=PYTHONPATH=/Users/<USER>/Documents/codes/agentic-feynman

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/Users/<USER>/Documents/codes/agentic-feynman/temp_uploads
ReadWritePaths=/Users/<USER>/Documents/codes/agentic-feynman/temp_uploads
ReadWritePaths=/Users/<USER>/Documents/codes/agentic-feynman/temp_uploads

[Install]
WantedBy=multi-user.target
